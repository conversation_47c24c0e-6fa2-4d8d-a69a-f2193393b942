import { gql } from 'graphql-request';

// --- Queries ---

export const GET_MARKETPLACE = gql`
  query Marketplace($id: ID!, $input: ProceduresInput!) {
    marketplace(id: $id) {
      id
      mgId
      name
      navigationGroupId
      providesAtHome
      logo
      favicon
      primaryColor
      paymentSettings {
        paymentCollectionMethod
        paymentDepositType
        paymentDepositValue
        hasPaymentPolicy
        paymentPolicyName
        paymentPolicyText
        requirePaymentPolicyAttestation
      }
      procedures(input: $input) {
        id
        name
        tagline
        ingredients
        description
        thumbnail
        price
        duration
        addOns
      }
      procedureGroups(input: $input) {
        id
        name
        thumbnail
        description
        procedureBaseDefGroups
        procedureBaseDefs
        banner
      }
      clinicOrganizations {
        id
        name
        address
        phone
      }
      packages {
        id
        price
        name
        description
        list
        packageItemDefinitions {
          id
          packageId
          points
          procedureGroups
        }
      }
      membershipDefinitions {
        id
        price
        name
        period
        packageId
        marketplaceId
        description
        list
        discounts {
          id
          membershipDefinitionId
          percentage
          procedureGroups
        }
      }
    }
  }
`;

export const GET_MEMBERSHIP_DISCOUNT = gql`
  query GetMembershipCreditDiscount($input: MembershipCreditDiscountInput!) {
    getMembershipCreditDiscount(input: $input) {
      credit
      discount
    }
  }
`;

export const GET_CHECKOUT_SUMMARY = gql`
  query CheckoutSummary($input: CheckoutSummaryInput!) {
    checkoutSummary(input: $input) {
      procedures
      membership
      credit
      discount
      promoCode
      promoCodeText
      gratuity
      travelFee
      total
    }
  }
`;

export const VALIDATE_PROMO_CODE = gql`
  query ValidatePromoCode($input: ValidatePromoCodeInput!) {
    validatePromoCode(input: $input) {
      valid
      error
      discountAmount
    }
  }
`;

export const GET_PROCEDURE_FULL = gql`
  query Marketplace($id: ID!, $input: ProcedureInput!) {
    marketplace(id: $id) {
      procedure(input: $input) {
        id
        name
        description
        duration
        price
        tags
        addOns
        ingredients
      }
    }
  }
`;

export const GET_AVAILABILITIES = gql`
  query GetAvailabilities($id: ID!, $input: AvailabilityInput!) {
    marketplace(id: $id) {
      availabilities(input: $input)
    }
  }
`;

export const GET_USER = gql`
  query GetUser($mgId: ID!) {
    viewer(mgId: $mgId) {
      createdAt
      updatedAt
      id
      emailConfirmed
      phoneConfirmed
      profile {
        address
        dob
        email
        familyName
        givenName
        phone
        tzid
      }
      memberships {
        id
        membershipDefinitionId
        renewalDate
        status
      }
      primaryInstrumentId
      paymentInstruments {
        id
        type
        processor
        brand
        lastFour
        expirationMonth
        expirationYear
      }
      packageItems {
        id
        packageItemDefinitionId
        membershipId
        totalPoints
        packageName
        expiresAt
        balance
      }
    }
  }
`;

export const GET_USER_APPOINTMENTS = gql`
  query ProfileAppointments($id: ID!) {
    profileAppointments(marketplaceId: $id) {
      id
      start
      end
      duration
      location
      startedAt
      completedAt
      procedureBaseDefs
      organizationPhone
      practitioner
      status
    }
  }
`;

export const LOGOUT = gql`
  mutation Logout {
    logout
  }
`;

// --- Mutations ---

// Authentication
export const EMAIL_REQUEST_CONFIRMATION = gql`
  mutation RequestConfirmEmail($id: String!) {
    requestConfirmEmail(marketplaceId: $id)
  }
`;

export const EMAIL_CONFIRM = gql`
  mutation ConfirmEmail($input: ConfirmEmailInput!) {
    confirmEmail(input: $input)
  }
`;

export const PHONE_REQUEST_CONFIRMATION = gql`
  mutation RequestConfirmPhone($id: String!) {
    requestConfirmPhone(marketplaceId: $id)
  }
`;

export const PHONE_CONFIRM = gql`
  mutation ConfirmPhone($input: ConfirmPhoneInput!) {
    confirmPhone(input: $input)
  }
`;

export const REQUEST_RESET_PASSWORD = gql`
  mutation RequestResetPassword($input: RequestResetPasswordInput!) {
    requestResetPassword(input: $input)
  }
`;

export const RESET_PASSWORD_BY_TOKEN = gql`
  mutation ResetPasswordByToken($input: ResetPasswordByTokenInput!) {
    resetPasswordByToken(input: $input)
  }
`;

export const EMAIL_SIGNUP = gql`
  mutation Register($input: RegisterInput!) {
    register(input: $input) {
      user {
        id
        email
        createdAt
      }
    }
  }
`;

export const PASSCODE_AUTHENTICATE = gql`
  mutation PasscodeAuthenticate($input: PasscodeAuthenticateInput!) {
    passcodeAuthenticate(input: $input) {
      accessToken
      user {
        id
        createdAt
        updatedAt
        emailConfirmed
        phoneConfirmed
        profile {
          address
          dob
          email
          familyName
          givenName
          phone
          tzid
        }
        memberships {
          id
          renewalDate
          status
        }
        primaryInstrumentId
        paymentInstruments {
          id
          type
          processor
          brand
          lastFour
          expirationMonth
          expirationYear
        }
      }
    }
  }
`;

//This is a query but used as mutation to called on-demand and not on page load
export const GET_AUTH_OPTIONS = gql`
  query AuthOptions($input: AuthOptionsInput!) {
    authOptions(input: $input)
  }
`;

export const PASSCODE_REQUEST = gql`
  mutation RequestPasscode($input: RequestPasscodeInput!) {
    requestPasscode(input: $input) {
      token
    }
  }
`;

export const EMAIL_LOGIN = gql`
  mutation Authenticate($input: AuthenticateInput!) {
    authenticate(input: $input) {
      user {
        id
        createdAt
        updatedAt
        emailConfirmed
        phoneConfirmed
        profile {
          address
          dob
          email
          familyName
          givenName
          phone
          tzid
        }
        memberships {
          id
          renewalDate
          status
        }
        primaryInstrumentId
        paymentInstruments {
          id
          type
          processor
          brand
          lastFour
          expirationMonth
          expirationYear
        }
      }
    }
  }
`;

export const GOOGLE_LOGIN = gql`
  mutation AuthenticateGoogle($input: GoogleAuthenticateInput!) {
    googleAuthenticate(input: $input) {
      token
      userData {
        id
        email
        emailConfirmed
        isPrivateEmail
        givenName
        familyName
        dob
        phone
      }
      authenticatePayload {
        user {
          id
          createdAt
          updatedAt
          emailConfirmed
          phoneConfirmed
          profile {
            address
            dob
            email
            familyName
            givenName
            phone
            tzid
          }
          memberships {
            id
            renewalDate
            status
          }
          primaryInstrumentId
          paymentInstruments {
            id
            type
            processor
            brand
            lastFour
            expirationMonth
            expirationYear
          }
        }
      }
    }
  }
`;

export const APPLE_LOGIN = gql`
  mutation AppleAuthenticate($input: AppleAuthenticateInput!) {
    appleAuthenticate(input: $input) {
      token
      userData {
        id
        email
        emailConfirmed
        isPrivateEmail
        givenName
        familyName
        dob
        phone
      }
      authenticatePayload {
        user {
          id
          createdAt
          updatedAt
          emailConfirmed
          phoneConfirmed
          profile {
            address
            dob
            email
            familyName
            givenName
            phone
            tzid
          }
          memberships {
            id
            renewalDate
            status
          }
          primaryInstrumentId
          paymentInstruments {
            id
            type
            processor
            brand
            lastFour
            expirationMonth
            expirationYear
          }
        }
      }
    }
  }
`;

export const THIRD_PARTY_REGISTER = gql`
  mutation RegisterWithToken($input: RegisterWithTokenInput!) {
    registerWithToken(input: $input) {
      user {
        id
        createdAt
        updatedAt
        emailConfirmed
        phoneConfirmed
        profile {
          address
          dob
          email
          familyName
          givenName
          phone
          tzid
        }
        memberships {
          id
          renewalDate
          status
        }
        primaryInstrumentId
        paymentInstruments {
          id
          type
          processor
          brand
          lastFour
          expirationMonth
          expirationYear
        }
      }
    }
  }
`;

export const UPDATE_PROFILE = gql`
  mutation UpdateProfile($input: UpdateProfileInput!) {
    updateProfile(input: $input) {
      updatedAt
      createdAt
    }
  }
`;

// Payment Instrument
export const ADD_PAYMENT_INSTRUMENT = gql`
  mutation AddPaymentInstrument($input: CreatePaymentInstrumentInput!) {
    createPaymentInstrument(input: $input) {
      id
      type
      processor
      brand
      lastFour
      expirationMonth
      expirationYear
    }
  }
`;

export const DELETE_PAYMENT_INSTRUMENT = gql`
  mutation DeletePaymentInstrument($id: ID!) {
    deletePaymentInstrument(id: $id)
  }
`;

export const UPDATE_PRIMARY_PAYMENT_INSTRUMENT = gql`
  mutation UpdatePrimaryPaymentInstrument($id: ID!) {
    updatePrimaryPaymentInstrument(input: { paymentInstrumentId: $id })
  }
`;

// Package
export const PURCHASE_PACKAGE = gql`
  mutation PurchasePackage($input: PurchasePackageInput!) {
    purchasePackage(input: $input) {
      id
      packageName
      expiresAt
      totalPoints
    }
  }
`;

//Membership

export const PURCHASE_MEMBERSHIP = gql`
  mutation CreateMembership($input: CreateMembershipInput!) {
    createMembership(input: $input) {
      id
      membershipDefinitionId
    }
  }
`;

export const CANCEL_MEMBERSHIP = gql`
  mutation CancelMembership($id: ID!) {
    cancelMembership(id: $id)
  }
`;

export const RENEW_MEMBERSHIP = gql`
  mutation ReactivateMembership($id: ID!) {
    reactivateMembership(id: $id) {
      id
      membershipDefinitionId
    }
  }
`;

// Appointments

export const CREATE_APPOINTMENT = gql`
  mutation CreateAppointmentRequest($input: CreateAppointmentRequestInput!) {
    createAppointmentRequest(input: $input) {
      id
      status
      practitioner
    }
  }
`;

// Apple Pay
export const CREATE_APPLE_PAY_SESSION = gql`
  mutation CreateApplePaySession($input: CreateApplePaySessionInput!) {
    createApplePaySession(input: $input)
  }
`;

export const CREATE_APPLE_PAY_PAYMENT = gql`
  mutation CreateApplePayInstrument($input: CreatePaymentInstrumentInput!) {
    createApplePayInstrument(input: $input) {
      id
      type
      processor
      brand
      lastFour
      expirationMonth
      expirationYear
    }
  }
`;
