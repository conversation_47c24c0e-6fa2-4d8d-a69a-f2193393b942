import { useMemo, useEffect } from 'react';
import { useHead } from 'unhead';

/**
 * Add cache-busting parameter to URL to force browser reload
 * @param {string} url - The original URL
 * @returns {string} - URL with cache-busting parameter
 */
const addCacheBuster = url => {
  if (!url) return url;
  const separator = url.includes('?') ? '&' : '?';
  return `${url}${separator}v=${Date.now()}`;
};

/**
 * Preload favicon to ensure it's available immediately
 * @param {string} faviconUrl - The favicon URL to preload
 */
const preloadFavicon = faviconUrl => {
  const preloadLink = document.createElement('link');
  preloadLink.rel = 'preload';
  preloadLink.as = 'image';
  preloadLink.href = addCacheBuster(faviconUrl);
  document.head.appendChild(preloadLink);
};

/**
 * Force favicon refresh by manipulating DOM directly
 * @param {string} faviconUrl - The favicon URL to set
 */
const forceFaviconRefresh = faviconUrl => {
  // First preload the favicon
  preloadFavicon(faviconUrl);

  // Remove existing favicon links (including those from the initial HTML)
  const existingLinks = document.querySelectorAll('link[rel*="icon"], link[rel="shortcut icon"]');
  existingLinks.forEach(link => {
    if (link.parentNode) {
      link.parentNode.removeChild(link);
    }
  });

  // Force a small delay to ensure DOM updates and preload completes
  setTimeout(() => {
    // Add new favicon link with cache buster
    const link = document.createElement('link');
    link.rel = 'icon';
    link.type = 'image/x-icon';
    link.href = addCacheBuster(faviconUrl);
    document.head.appendChild(link);

    // Also add shortcut icon for older browsers
    const shortcutLink = document.createElement('link');
    shortcutLink.rel = 'shortcut icon';
    shortcutLink.href = addCacheBuster(faviconUrl);
    document.head.appendChild(shortcutLink);

    // Add PNG version for better quality
    const pngLink = document.createElement('link');
    pngLink.rel = 'icon';
    pngLink.type = 'image/png';
    pngLink.href = addCacheBuster(faviconUrl);
    document.head.appendChild(pngLink);

    // Force browser to recognize the change by creating a temporary link
    const tempLink = document.createElement('link');
    tempLink.rel = 'icon';
    tempLink.href = 'data:image/x-icon;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQEAYAAABPYyMiAAAABmJLR0T///////8JWPfcAAAACXBIWXMAAABIAAAASABGyWs+AAAAF0lEQVRIx2NgGAWjYBSMglEwCkbBSAcACBAAAeaR9cIAAAAASUVORK5CYII=';
    document.head.appendChild(tempLink);
    setTimeout(() => {
      if (tempLink.parentNode) {
        tempLink.parentNode.removeChild(tempLink);
      }
    }, 50);
  }, 10);
};

/**
 * Custom hook to dynamically set the favicon based on marketplace data
 * @param {Object} marketplace - The marketplace object containing favicon URL
 * @param {string} marketplace.favicon - The URL of the custom favicon
 * @param {boolean} isLoading - Whether the marketplace data is still loading
 */
export const useDynamicFavicon = (marketplace, isLoading = false) => {
  // Determine the favicon URL to use
  const faviconUrl = useMemo(() => {
    if (isLoading) {
      return null; // Don't set favicon while loading
    }

    const customFavicon = marketplace?.favicon;
    if (customFavicon && typeof customFavicon === 'string' && customFavicon.trim()) {
      return customFavicon;
    }

    // Fallback to default favicon
    return '/favicon.ico';
  }, [marketplace?.favicon, isLoading]);

  // Force favicon refresh when URL changes
  useEffect(() => {
    if (faviconUrl) {
      forceFaviconRefresh(faviconUrl);
    }
  }, [faviconUrl]);

  // Also use the head hook as a backup method
  useHead({
    link: faviconUrl ? [
      {
        rel: 'icon',
        type: 'image/x-icon',
        href: addCacheBuster(faviconUrl),
        key: 'favicon-ico'
      },
      {
        rel: 'icon',
        type: 'image/png',
        href: addCacheBuster(faviconUrl),
        key: 'favicon-png'
      },
      {
        rel: 'shortcut icon',
        href: addCacheBuster(faviconUrl),
        key: 'favicon-shortcut'
      }
    ] : []
  });
};

export default useDynamicFavicon;
