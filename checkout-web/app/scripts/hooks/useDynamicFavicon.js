import { useEffect, useMemo } from 'react';
import { useHead } from 'unhead';

/**
 * Custom hook to dynamically set the favicon based on marketplace data
 * @param {Object} marketplace - The marketplace object containing favicon URL
 * @param {string} marketplace.favicon - The URL of the custom favicon
 * @param {boolean} isLoading - Whether the marketplace data is still loading
 */
export const useDynamicFavicon = (marketplace, isLoading = false) => {
  // Determine the favicon URL to use
  const faviconUrl = useMemo(() => {
    if (isLoading) {
      return null; // Don't set favicon while loading
    }

    const customFavicon = marketplace?.favicon;
    if (customFavicon && typeof customFavicon === 'string' && customFavicon.trim()) {
      return customFavicon;
    }

    // Fallback to default favicon
    return '/favicon.ico';
  }, [marketplace?.favicon, isLoading]);

  // Use the head hook to set the favicon
  useHead({
    link: faviconUrl ? [
      {
        rel: 'icon',
        type: 'image/x-icon',
        href: faviconUrl
      },
      {
        rel: 'icon',
        type: 'image/png',
        href: faviconUrl
      },
      {
        rel: 'shortcut icon',
        href: faviconUrl
      }
    ] : []
  });
};

export default useDynamicFavicon;
