import { useMutation, useQuery } from '@tanstack/react-query';
import React, { useEffect, useRef, useState } from 'react';
import { Outlet } from 'react-router-dom';

import AppBar from '../../components/appBar/appBar';
import AppBarBrand from '../../components/appBar/appBarBrand';
import Badge from '../../components/badge/badge';
import Button from '../../components/controls/button/button';
import IconButton from '../../components/controls/iconButton/iconButton';
import Drawer from '../../components/drawer/drawer';
import AlertCard from '../../components/nomadMD/alertCard/alertCard';
import AuthFlow from '../../components/nomadMD/authFlow/authFlow';
import Dialog from '../../components/nomadMD/dialog/dialog';
import ProcedureList from '../../components/nomadMD/procedureList/procedureList';
import Toolbar from '../../components/toolbar/toolbar';
import ToolbarItem from '../../components/toolbar/toolbarItem';
import { EventMessages, SessionStorageKeys } from '../../constants';
import Events from '../../events';
import withRouter from '../../hoc/withRouter';
import { useAppNavigate } from '../../hooks/useAppNavigate';
import { useDynamicFavicon } from '../../hooks/useDynamicFavicon';
import { useSelectedLocation } from '../../hooks/useSelectedLocation';
import { useSelectedProcedures } from '../../hooks/useSelectedProcedures';
import { useSession } from '../../hooks/useStorage';
import NomadMDService from '../../services/nomadService';
import Session from '../../session';
import bemify from '../../utils/bemUtils';
import { debounce } from '../../utils/debounceUtils';
import { geocodeAddress } from '../../utils/geoUtils';
import { isLoadingMutation } from '../../utils/networkUtils';

/**
 * Represents the main layout view of the app.
 */
const MainLayout = ({ jumboHeader = false }) => {
  const appNavigate = useAppNavigate();

  const appBarRef = useRef(null);
  const showStickyAppBar = useRef(false);
  const lastLocation = useRef(null);
  const lastProcedures = useRef(null);

  const [openAuthFlow, setOpenAuthFlow] = useState(false);
  const [openCart, setOpenCart] = useState(false);
  const [showCartClearedDialog, setShowCartClearedDialog] = useState(false);

  const marketplaceQuery = useQuery({
    queryKey: NomadMDService.getMarketplace.key(),
    queryFn: () => NomadMDService.getMarketplace.function()
  });

  const marketplace = marketplaceQuery.data;

  // Set dynamic favicon based on marketplace data
  useDynamicFavicon(marketplace, marketplaceQuery.isLoading);

  /** @type {User?} */
  const userSession = useSession({
    defaultValue: null,
    key: SessionStorageKeys.user
  });

  const selectedLocation = useSelectedLocation();
  const selectedProcedures = useSelectedProcedures();

  /** @type {Availability} */
  const selectedDateTime = useSession({
    key: SessionStorageKeys.selectedDateTime,
    defaultValue: []
  });

  /** @type {VirtualAvailability[]} */
  const selectedWaitlist = useSession({
    //reset: true,
    key: SessionStorageKeys.selectedWaitlist,
    defaultValue: []
  });

  /** @type {boolean} */
  const alertVerifyEmail = useSession({
    key: SessionStorageKeys.alertVerifyEmail
  });

  /** @type {boolean} */
  const alertVerifyPhone = useSession({
    key: SessionStorageKeys.alertVerifyPhone
  });

  const emailVerificationMutation = useMutation({
    mutationFn: NomadMDService.requestEmailConfirmation.function
  });

  const logoutMutation = useMutation({
    mutationFn: NomadMDService.logout.function
  });

  // Clear selections when location changes
  useEffect(() => {
    const currentLocation = selectedLocation.location ? JSON.stringify(selectedLocation.location) : null;

    if (lastLocation.current && currentLocation !== lastLocation.current) {
      if (selectedProcedures.procedures?.length) {
        setShowCartClearedDialog(true);
      }

      selectedProcedures.clear();
      Session.setItem(SessionStorageKeys.selectedDateTime, null);
      Session.setItem(SessionStorageKeys.selectedWaitlist, []);
    }

    lastLocation.current = currentLocation;
  }, [selectedLocation.location, selectedProcedures]);

  // Clear selectedDateTime when procedures change
  useEffect(() => {
    const currentProcedures = selectedProcedures.procedures ? JSON.stringify(selectedProcedures.procedures) : null;

    if (lastProcedures.current && currentProcedures !== lastProcedures.current) {
      Session.setItem(SessionStorageKeys.selectedDateTime, null);
    }

    lastProcedures.current = currentProcedures;
  }, [selectedProcedures.procedures]);

  // Auto-geocode user's profile address when logged in and has an address
  useEffect(() => {
    if (userSession?.profile?.address && !selectedLocation.location && marketplace?.providesAtHome) {
      const processUserAddress = async () => {
        try {
          const geocodes = await geocodeAddress(userSession.profile.address);

          if (geocodes) {
            selectedLocation.setGeolocation({
              address: geocodes.value,
              latitude: geocodes.lat,
              longitude: geocodes.lng
            });

            appNavigate('/services', { replace: false }, ['procedure']);
          }
        } catch (error) {
          console.warn('Failed to auto-geocode user address:', error);
        }
      };

      processUserAddress();
    }
  }, [userSession?.profile?.address, marketplace?.providesAtHome, selectedLocation, appNavigate]);

  useEffect(() => {
    if (marketplace?.id && marketplace?.clinicOrganizations) {
      if (marketplace.clinicOrganizations.length === 1 && !marketplace.providesAtHome) {
        // Single location, no at-home services
        const [clinic] = marketplace.clinicOrganizations;

        selectedLocation.setClinic({
          organizationId: clinic.id,
          name: clinic.name,
          address: clinic.address,
          phone: clinic.phone
        });
      } else if (selectedLocation.location) {
        const selectedId = selectedLocation.location.organizationId;

        // if clinic is selected, check that it's valid
        if (selectedId && !marketplace.clinicOrganizations.find(({ id }) => id === selectedId)) {
          selectedLocation.clear();
        }
      }
    }
  }, [marketplace, selectedLocation]);

  const safeWindowScroll = debounce(() => onWindowScroll(), 1);

  useEffect(() => {
    window.addEventListener('scroll', safeWindowScroll);

    return () => {
      window.removeEventListener('scroll', safeWindowScroll);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    Session.setItem(SessionStorageKeys.alertVerifyPhone, !userSession?.phoneConfirmed);
  }, [userSession?.phoneConfirmed]);

  useEffect(() => {
    if (marketplace?.primaryColor) {
      document.documentElement.style.setProperty('--sk-theme-primary', marketplace.primaryColor);

      const hexToRgb = hex => {
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        return result
          ? {
              r: parseInt(result[1], 16),
              g: parseInt(result[2], 16),
              b: parseInt(result[3], 16)
            }
          : null;
      };

      const rgbToHex = (r, g, b) => {
        return `#${((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)}`;
      };

      const darkenColor = (hex, amount = 20) => {
        const rgb = hexToRgb(hex);
        if (!rgb) return hex;

        const r = Math.max(0, rgb.r - amount);
        const g = Math.max(0, rgb.g - amount);
        const b = Math.max(0, rgb.b - amount);

        return rgbToHex(r, g, b);
      };

      const lightenColor = (hex, amount = 30) => {
        const rgb = hexToRgb(hex);
        if (!rgb) return hex;

        const r = Math.min(255, rgb.r + amount);
        const g = Math.min(255, rgb.g + amount);
        const b = Math.min(255, rgb.b + amount);

        return rgbToHex(r, g, b);
      };

      const isColorDark = hex => {
        const rgb = hexToRgb(hex);
        if (!rgb) return false;

        const luminance = (0.299 * rgb.r + 0.587 * rgb.g + 0.114 * rgb.b) / 255;
        return luminance < 0.3;
      };

      const hoverColor = isColorDark(marketplace.primaryColor)
        ? lightenColor(marketplace.primaryColor)
        : darkenColor(marketplace.primaryColor);

      document.documentElement.style.setProperty('--sk-theme-primary-hover', hoverColor);
    }
  }, [marketplace?.primaryColor]);

  const toggleCart = () => {
    setOpenCart(prevShowAppointmentsState => !prevShowAppointmentsState);
  };

  const handleCartCleared = () => {
    setShowCartClearedDialog(false);
  };

  const onWindowScroll = () => {
    const actualAppBar = appBarRef.current;

    if (window.pageYOffset > actualAppBar?.offsetHeight) {
      if (!showStickyAppBar.current) {
        showStickyAppBar.current = true;

        actualAppBar?.classList.toggle('app-bar--hidden');

        const timeout = setTimeout(() => {
          clearTimeout(timeout);

          actualAppBar?.classList.toggle('app-bar--sticky');
        }, 250);

        Events.emit(EventMessages.showStickyHeader);
      }
    } else {
      showStickyAppBar.current = false;

      actualAppBar?.classList.remove('app-bar--hidden');
      actualAppBar?.classList.remove('app-bar--sticky');

      Events.emit(EventMessages.hideStickyHeader);
    }
  };

  const [block, element] = bemify('main-layout');
  const [actionButton] = bemify('action-button');

  const canCheckout = Boolean(
    selectedLocation.location &&
      selectedProcedures.procedures?.length &&
      ((selectedDateTime?.date && selectedDateTime?.time) || selectedWaitlist?.length)
  );

  const { name = '', logo } = marketplace || {};

  return (
    <div className={block(jumboHeader ? 'jumbo-header' : '')}>
      <AppBar
        ref={appBarRef}
        brand={
          <AppBarBrand link="/">
            {!logo ? (
              <div style={{ width: 40, height: 40 }} />
            ) : (
              <img
                className={element('logo')}
                src={logo}
                alt={name}
              />
            )}
          </AppBarBrand>
        }
        toolbar={
          <Toolbar>
            {!!selectedProcedures.procedures?.length && (
              <ToolbarItem>
                <Badge content={selectedProcedures.procedures.length}>
                  <IconButton onClick={toggleCart}>
                    <span className="material-symbols-outlined">calendar_today</span>
                  </IconButton>
                </Badge>
              </ToolbarItem>
            )}
            <ToolbarItem>
              <IconButton
                classes={element('profile-button')}
                onClick={() => {
                  if (userSession?.id) {
                    Session.setItem(SessionStorageKeys.accountManagementTab, 'profile');
                    appNavigate('/profile');
                  } else {
                    setOpenAuthFlow(true);
                  }
                }}>
                <span className="material-symbols-outlined">person</span>
              </IconButton>
            </ToolbarItem>
          </Toolbar>
        }
      />

      {userSession && alertVerifyPhone && !userSession.phoneConfirmed && (
        <div className={element('phone-confirmation')}>
          <AlertCard
            classes={element('phone-confirmation-card')}
            description={
              <React.Fragment>
                <p>Your phone number has not been verified.</p>
                <p>
                  Please&nbsp;
                  <Button
                    classes={element('phone-confirmation-button')}
                    href="/confirm-phone"
                    variant="link">
                    click here
                  </Button>
                  &nbsp; to verify it and receive updates
                </p>
              </React.Fragment>
            }
            onClose={() => {
              Session.setItem(SessionStorageKeys.alertVerifyPhone, false);
            }}
          />
        </div>
      )}

      <Dialog
        open={alertVerifyEmail}
        onClose={() => {
          Session.setItem(SessionStorageKeys.alertVerifyEmail, false);
          appNavigate('/');
        }}>
        <Dialog.Header>Pending Email Verification</Dialog.Header>
        <Dialog.Body>
          <p>
            Please verify your email address to access all features of the application. Request a new verification email
            to be sent to your email address.
          </p>
        </Dialog.Body>
        <Dialog.Footer>
          <Button
            variant="secondary"
            onClick={() => {
              logoutMutation.mutate();
            }}>
            Logout
          </Button>
          <Button
            loading={isLoadingMutation(emailVerificationMutation)}
            variant="primary"
            onClick={() => {
              emailVerificationMutation.mutate();
            }}>
            Send Email
          </Button>
        </Dialog.Footer>
      </Dialog>

      <Dialog
        open={showCartClearedDialog}
        onClose={() => setShowCartClearedDialog(false)}>
        <Dialog.Header>Location Changed</Dialog.Header>
        <Dialog.Body>
          <p>
            You&apos;ve changed the service address. Your cart has been cleared since services may not be available at
            the new location.
          </p>
        </Dialog.Body>
        <Dialog.Footer>
          <Button
            variant="primary"
            onClick={handleCartCleared}>
            Continue
          </Button>
        </Dialog.Footer>
      </Dialog>

      <main className={element('content')}>{<Outlet />}</main>

      <Drawer
        show={openCart}
        onClose={() => setOpenCart(false)}
        header={
          <button
            type="button"
            className={element('close-button')}
            data-drawer-close
            onClick={toggleCart}>
            <span className="material-symbols-outlined">close</span>
          </button>
        }
        body={
          <React.Fragment>
            <h4 className={element('services-heading')}>Selected Services</h4>
            <ProcedureList
              procedures={selectedProcedures.procedures}
              onClickEdit={procedure => {
                setOpenCart(false);
                appNavigate(`/services?procedure=${procedure.id}`);
              }}
              onClickRemove={procedure => {
                selectedProcedures.removeProcedure(procedure.id);
              }}
            />
            <div className={element('services-actions')}>
              {canCheckout && (
                <Button
                  variant="primary"
                  classes={actionButton()}
                  onClick={() => {
                    setOpenCart(false);
                    appNavigate('/confirm');
                  }}>
                  Checkout
                </Button>
              )}
            </div>
          </React.Fragment>
        }
      />

      <AuthFlow
        modal
        redirect={`/${window.location.pathname.split('/').slice(2).join('/')}${window.location.search}`}
        open={openAuthFlow}
        onClose={() => setOpenAuthFlow(false)}
      />
    </div>
  );
};

export default withRouter(MainLayout);
