# -----------------------------------------------
# !!! THIS FILE WAS GENERATED BY TYPE-GRAPHQL !!!
# !!!   DO NOT MODIFY THIS FILE BY YOURSELF   !!!
# -----------------------------------------------

input AppleAuthenticateInput {
  clientId: String!
  code: String!
  mgId: ID!
}

type Appointment {
  completedAt: DateTime

  """Appointment length in minutes"""
  duration: Int
  end: [String!]
  id: String!
  location: String!
  organizationPhone: String
  practitioner: String
  procedureBaseDefs: [String!]!
  start: [String!]
  startedAt: DateTime
  status: AppointmentStatus!
}

enum AppointmentStatus {
  BOOKED
  COMPLETE
  PENDING
}

input AppointmentTimeRangeInput {
  end: DateTime!
  start: DateTime!
}

input AuthOptionsInput {
  """Currently only email addresses are accepted as valid identifiers"""
  identifier: String!
  mgId: ID!
}

input AuthenticateInput {
  email: String!
  mgId: ID!
  password: String!
}

type AuthenticatePayload {
  accessToken: String!
  refreshToken: String!
  resetPasswordToken: String
  user: User!
}

"""Supported authentication methods"""
enum AuthenticationMethod {
  OAUTH_APPLE
  OAUTH_GOOGLE
  PASSCODE_EMAIL
  PASSCODE_PHONE
  PASSWORD
}

input AvailabilityInput {
  dateRange: [DateTime!]!
  location: GeolocationInput
  organizationId: ID
  procedures: [String!]!
}

input CancelAppointmentInput {
  appointmentId: ID!
  reason: String
}

type CheckoutSummary {
  credit: [Int!]!
  discount: [Int!]!
  gratuity: Int!
  membership: Int!
  procedures: [Int!]!
  promoCode: [Int!]
  promoCodeText: String
  total: [Int!]!
  travelFee: [Int!]!
}

input CheckoutSummaryInput {
  address: String
  gratuity: Int
  marketplaceId: ID!
  membershipDefinitionId: ID

  """
  Overrides the address if provided. The organization must be enabled for in-clinic visits.
  """
  organizationId: ID
  procedureIds: [ID!]!

  """Optional promo code to apply"""
  promoCode: String
}

input ConfirmEmailInput {
  marketplaceId: ID!
  token: String!
}

input ConfirmPhoneInput {
  code: String!
  marketplaceId: ID!
}

input CreateApplePaySessionInput {
  displayName: String!
  domain: String!
  validationUrl: String!
}

input CreateAppointmentRequestInput {
  dateRanges: [AppointmentTimeRangeInput!]!
  gratuity: Float!
  location: String
  marketplaceId: ID!
  membershipDefinitionId: ID
  notes: String
  paymentMethod: PaymentMethodInput!
  procedures: [String!]!
  promoCode: String
  registerUser: RegisterInput
  selectedOrgId: ID
}

input CreateMarketplaceConfigInput {
  marketplaceId: ID!
}

input CreateMembershipInput {
  marketplaceId: ID!
  membershipDefinitionId: ID!
  paymentInstrumentId: ID!
}

input CreatePaymentInstrumentInput {
  card: PaymentMethodCardInput!
  save: Boolean
}

"""
The javascript `Date` as string. Type represents date and time as the ISO Date string.
"""
scalar DateTime

type Discount {
  id: ID!
  membershipDefinitionId: ID!
  percentage: Int!
  procedureGroups: [ID!]!
}

input GeolocationInput {
  latitude: Float!
  longitude: Float!
}

input GoogleAuthenticateInput {
  clientId: String!
  code: String!
  mgId: ID!
  redirectUrl: String!
}

type Marketplace {
  availabilities(input: AvailabilityInput!): [String!]!
  clinicOrganizations: [Organization!]!
  favicon: String
  id: ID!
  logo: String
  membershipDefinitions: [MembershipDefinition!]!
  mgId: String!
  name: String!
  navigationGroupId: ID
  packages: [Package!]!
  paymentSettings: PaymentSettings
  primaryColor: String
  procedure(input: ProcedureInput!): Procedure!
  procedureGroups(input: ProceduresInput!): [ProcedureGroup!]!
  procedures(input: ProceduresInput!): [Procedure!]!
  providesAtHome: Boolean!
  travelFees(input: TravelFeeInput!): [Int!]!
}

type MarketplaceConfig {
  archivedAt: DateTime
  createdAt: DateTime!
  marketplace: Marketplace
  marketplaceId: ID!
  updatedAt: DateTime!
}

type Membership {
  id: ID!
  marketplaceUserId: ID!
  membershipDefinitionId: ID!
  packageItems: [PackageItem!]
  renewalDate: DateTime!
  status: MembershipStatusType!
}

type MembershipDefinition {
  advertise: Boolean
  description: String
  discounts: [Discount!]
  id: ID!
  list: String
  marketplaceId: ID!
  name: String!
  packageId: ID
  period: MembershipDefinitionPeriodType!
  price: Int!
}

enum MembershipDefinitionPeriodType {
  Monthly
  Quarterly
  Yearly
}

enum MembershipStatusType {
  Active
  Cancelled
  Inactive
  PaymentError
}

type Mutation {
  appleAuthenticate(input: AppleAuthenticateInput!): OAuthPayload!
  authenticate(input: AuthenticateInput!): AuthenticatePayload!
  authenticateAs(userId: ID!): AuthenticatePayload!
  cancelAppointment(input: CancelAppointmentInput!): Boolean!
  cancelMembership(id: ID!): Boolean!
  confirmEmail(input: ConfirmEmailInput!): Boolean!
  confirmPhone(input: ConfirmPhoneInput!): Boolean!
  createApplePayInstrument(input: CreatePaymentInstrumentInput!): PaymentInstrument!
  createApplePaySession(input: CreateApplePaySessionInput!): String
  createAppointmentRequest(input: CreateAppointmentRequestInput!): Appointment!
  createMarketplaceConfig(input: CreateMarketplaceConfigInput!): MarketplaceConfig!
  createMembership(input: CreateMembershipInput!): Membership!
  createPaymentInstrument(input: CreatePaymentInstrumentInput!): PaymentInstrument!
  deletePaymentInstrument(id: ID!): Boolean
  googleAuthenticate(input: GoogleAuthenticateInput!): OAuthPayload!
  logout: Boolean!
  passcodeAuthenticate(input: PasscodeAuthenticateInput!): AuthenticatePayload!
  purchasePackage(input: PurchasePackageInput!): [PackageItem!]!
  reactivateMembership(id: ID!): Membership!
  refreshTokens(refreshToken: String!): AuthenticatePayload!
  register(input: RegisterInput!): AuthenticatePayload!
  registerWithToken(input: RegisterWithTokenInput!): AuthenticatePayload!
  requestConfirmEmail(marketplaceId: String!): Boolean!
  requestConfirmPhone(marketplaceId: String!): Boolean!
  requestPasscode(input: RequestPasscodeInput!): RequestPasscodePayload!
  requestResetPassword(input: RequestResetPasswordInput!): Boolean!
  resetPasswordByToken(input: ResetPasswordByTokenInput!): Boolean!
  revokeRefreshTokens(userId: ID): Boolean!
  updatePrimaryPaymentInstrument(input: UpdatePrimaryPaymentInstrumentInput!): Boolean!
  updateProfile(input: UpdateProfileInput!): User!
}

type OAuthPayload {
  authenticatePayload: AuthenticatePayload
  token: String
  userData: OAuthUserData
}

type OAuthUserData {
  dob: String
  email: String
  emailConfirmed: Boolean
  familyName: String
  givenName: String
  id: String!
  isPrivateEmail: Boolean
  phone: String
}

type Organization {
  address: String
  id: ID!
  name: String!
  phone: String
}

type Package {
  advertise: Boolean
  description: String
  id: ID!
  list: String
  name: String!
  packageItemDefinitions: [PackageItemDefinition!]
  price: Int!
}

type PackageItem {
  balance: Int!
  expiresAt: DateTime
  groupNames: String!
  id: ID!
  membershipId: ID
  packageItemDefinitionId: ID!
  packageName: String!
  totalPoints: Int!
}

type PackageItemDefinition {
  id: ID!
  packageId: ID!
  points: Int!
  procedureGroups: [ID!]
}

input PasscodeAuthenticateInput {
  code: String!
  token: String!
}

enum PaymentAccountPlatform {
  Finix
}

input PaymentIdentityInput {
  email: String
  firstName: String
  lastName: String
  phone: String
  postalCode: String
}

type PaymentInstrument {
  brand: String
  expirationMonth: Int
  expirationYear: Int
  id: ID!
  lastFour: String
  processor: PaymentAccountPlatform!
  type: PaymentInstrumentType!
}

enum PaymentInstrumentType {
  Card
}

input PaymentMethodCardInput {
  identity: PaymentIdentityInput
  thirdPartyToken: String
  token: String
}

input PaymentMethodInput {
  card: PaymentMethodCardInput
  paymentInstrumentId: ID
  save: Boolean
}

type PaymentSettings {
  hasPaymentPolicy: Boolean
  paymentCollectionMethod: String
  paymentDepositType: String
  paymentDepositValue: Float
  paymentPolicyName: String
  paymentPolicyText: String
  requirePaymentPolicyAttestation: Boolean
}

type Permission {
  id: ID!
  resource: PermissionResource!
  resourceId: String!
  value: String!
}

enum PermissionResource {
  MARKETPLACE
  MARKETPLACE_GROUP
  ROOT
}

type Procedure {
  addOns: [String!]!
  category: String
  description: String!
  duration: [Int!]!
  id: ID!
  ingredients: String
  layouts: [ProcedureLayout!]!
  name: String!
  organizationIds: [ID!]
  price: [Float!]!
  tagline: String
  tags: [String!]!
  thumbnail: String
}

enum ProcedureBaseDefinitionLayoutType {
  Block
  Faq
  Grid
  List
}

type ProcedureGroup {
  banner: String
  bgcolor: String
  description: String
  featuredBaseDefs: [String!]
  fontColor: String
  id: ID!
  name: String!
  procedureBaseDefGroups: [String!]
  procedureBaseDefs: [String!]
  thumbnail: String
}

input ProcedureInput {
  id: ID!
  location: GeolocationInput
  organizationId: ID
}

type ProcedureLayout {
  id: ID!
  itemIndex: Float!
  layout: String!
  procedureBaseDefinitionId: ID!
  type: ProcedureBaseDefinitionLayoutType!
}

input ProceduresInput {
  location: GeolocationInput
  organizationId: ID
}

type Profile {
  address: String!
  dob: String!
  email: String!
  familyName: String!
  givenName: String!
  id: ID!
  phone: String!
  sexAssignedAtBirth: String
  tzid: String!
}

input ProfileRequiredInput {
  address: String

  """yyyy-mm-dd"""
  dob: String!
  familyName: String!
  givenName: String!
  phone: String!
  sexAssignedAtBirth: String
  tzid: String!
}

type PromoCodeValidationResult {
  """Discount amount in cents"""
  discountAmount: Int
  error: String
  valid: Boolean!
}

input PurchasePackageInput {
  marketplaceId: ID!
  packageId: ID!
  paymentMethod: PaymentMethodInput!
}

type Query {
  authOptions(input: AuthOptionsInput!): [AuthenticationMethod!]
  checkoutSummary(input: CheckoutSummaryInput!): CheckoutSummary!
  marketplace(id: ID!): Marketplace!
  marketplaceConfigs: [MarketplaceConfig!]!
  marketplaces: [Marketplace!]!
  profileAppointments(marketplaceId: ID!): [Appointment!]!
  user(id: ID!): User
  users: [User!]!
  validatePromoCode(input: ValidatePromoCodeInput!): PromoCodeValidationResult!
  viewer(mgId: ID!): User
}

input RegisterInput {
  email: String!
  mgId: ID!
  optedIn: Boolean = false
  password: String
  profile: ProfileRequiredInput
}

input RegisterWithTokenInput {
  email: String!
  optedIn: Boolean = false
  privateEmail: String
  profile: ProfileRequiredInput!
  token: String!
}

input RequestPasscodeInput {
  email: String!
  marketplaceId: ID!
}

type RequestPasscodePayload {
  token: String!
}

input RequestResetPasswordInput {
  email: String!
  mgId: ID!
  webOriginUrl: String!
}

input ResetPasswordByTokenInput {
  mgId: ID!
  password: String!
  token: String!
  webOriginUrl: String!
}

input TravelFeeInput {
  location: GeolocationInput!
  organizationIds: [ID!]!
}

input UpdatePrimaryPaymentInstrumentInput {
  paymentInstrumentId: ID!
}

input UpdateProfileInput {
  address: String

  """yyyy-mm-dd"""
  dob: String
  familyName: String
  givenName: String
  phone: String
  sexAssignedAtBirth: String
  tzid: String
  userId: ID!
}

type User {
  archivedAt: DateTime
  createdAt: DateTime!
  email: String!
  emailConfirmed: Boolean!
  id: ID!
  memberships: [Membership!]!
  mgId: ID!
  packageItems: [PackageItem!]!
  paymentInstruments: [PaymentInstrument!]!
  permissions: [Permission!]!
  phone: String
  phoneConfirmed: Boolean!
  primaryInstrumentId: ID
  profile: Profile
  updatedAt: DateTime!
}

input ValidatePromoCodeInput {
  """Booking amount in cents"""
  bookingAmount: Int
  bookingDate: String
  code: String!
  marketplaceId: ID!
  organizationId: ID
  procedureIds: [ID!]
}
