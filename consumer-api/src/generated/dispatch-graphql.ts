import { GraphQLClient } from 'graphql-request';
import * as Dom from 'graphql-request/dist/types.dom';
import gql from 'graphql-tag';
export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: string;
  String: string;
  Boolean: boolean;
  Int: number;
  Float: number;
  DateTime: any;
};

export type AcceptAppointmentInput = {
  appointmentId: Scalars['ID'];
  profileId: Scalars['ID'];
  start: Scalars['DateTime'];
};

export type AddOrganizationsToMarketplaceInput = {
  marketplaceId: Scalars['ID'];
  organizationIds: Array<Scalars['ID']>;
};

export type AllergyIntolerance = {
  __typename?: 'AllergyIntolerance';
  createdAt: Scalars['DateTime'];
  id: Scalars['ID'];
  name: Scalars['String'];
  updatedAt: Scalars['DateTime'];
};

export type ApiKey = {
  __typename?: 'ApiKey';
  createdAt: Scalars['DateTime'];
  id: Scalars['ID'];
  kid: Scalars['ID'];
  name: Scalars['String'];
  roles: Array<Role>;
  updatedAt: Scalars['DateTime'];
  userId: Scalars['ID'];
};

export type Appointment = {
  __typename?: 'Appointment';
  alternateTimes: Array<Scalars['DateTime']>;
  archivedAt?: Maybe<Scalars['DateTime']>;
  cancelReason?: Maybe<Scalars['String']>;
  checkout?: Maybe<Checkout>;
  completedAt?: Maybe<Scalars['DateTime']>;
  createdAt: Scalars['DateTime'];
  createdBy?: Maybe<Scalars['ID']>;
  /** Appointment length in minutes */
  duration: Scalars['Int'];
  end: Scalars['DateTime'];
  id: Scalars['ID'];
  latitude?: Maybe<Scalars['Float']>;
  location: Scalars['String'];
  longitude?: Maybe<Scalars['Float']>;
  notes?: Maybe<Scalars['String']>;
  participants: Array<AppointmentParticipant>;
  procedureBaseDefs: Array<ProcedureBaseDefinition>;
  qualiphyInvitation?: Maybe<QualiphyInvitation>;
  start: Scalars['DateTime'];
  startedAt?: Maybe<Scalars['DateTime']>;
  status: AppointmentStatus;
  updatedAt: Scalars['DateTime'];
};

export type AppointmentCandidate = {
  __typename?: 'AppointmentCandidate';
  availableTimes: Array<Scalars['DateTime']>;
  id: Scalars['ID'];
  organization?: Maybe<Organization>;
  profile?: Maybe<Profile>;
  rank: Scalars['Int'];
  reservableTimes: Array<Scalars['DateTime']>;
  start?: Maybe<Scalars['DateTime']>;
};

export type AppointmentConstraint = {
  __typename?: 'AppointmentConstraint';
  appointments: Array<Appointment>;
  candidates: Array<AppointmentCandidate>;
  id: Scalars['ID'];
  organizations: Array<Organization>;
  profiles: Array<Profile>;
  timeRanges: Array<AppointmentTimeRange>;
};

export type AppointmentConstraintInput = {
  organizationIds?: InputMaybe<Array<Scalars['ID']>>;
  profileIds?: InputMaybe<Array<Scalars['ID']>>;
  timeRanges: Array<AppointmentTimeRangeInput>;
};

export type AppointmentFilterInput = {
  client_name?: InputMaybe<FilterStringInput>;
  duration?: InputMaybe<FilterIntegerInput>;
  id?: InputMaybe<FilterIntegerInput>;
  location?: InputMaybe<FilterStringInput>;
  orderApproved?: InputMaybe<Scalars['Boolean']>;
  practitioner_name?: InputMaybe<FilterStringInput>;
  procedure?: InputMaybe<FilterStringInput>;
  start?: InputMaybe<FilterDateInput>;
  status?: InputMaybe<FilterStringInput>;
};

export type AppointmentPage = {
  __typename?: 'AppointmentPage';
  data: Array<Appointment>;
  totalCount: Scalars['Int'];
};

export type AppointmentPageInput = {
  dateRange?: InputMaybe<Array<Scalars['DateTime']>>;
  filter?: InputMaybe<AppointmentFilterInput>;
  limit?: InputMaybe<Scalars['Int']>;
  offset?: InputMaybe<Scalars['Int']>;
  sort?: InputMaybe<Array<AppointmentSortInput>>;
};

export type AppointmentParticipant = {
  __typename?: 'AppointmentParticipant';
  clientProfileId?: Maybe<Scalars['ID']>;
  forms: Array<Form>;
  id: Scalars['ID'];
  name: Scalars['String'];
  orderApproved?: Maybe<Scalars['Boolean']>;
  participant?: Maybe<Participant>;
  profileId?: Maybe<Scalars['ID']>;
  responses: Array<AppointmentResponse>;
  status: ParticipationStatus;
  type: ParticipantType;
};

export type AppointmentParticipantInput = {
  id: Scalars['ID'];
  type: ParticipantType;
};

export enum AppointmentRefundType {
  Custom = 'CUSTOM',
  Full = 'FULL',
  FullMinusDeposit = 'FULL_MINUS_DEPOSIT',
  None = 'NONE'
}

export type AppointmentRequest = {
  __typename?: 'AppointmentRequest';
  archivedAt?: Maybe<Scalars['DateTime']>;
  cancelReason?: Maybe<Scalars['String']>;
  checkout?: Maybe<Checkout>;
  clientProfiles: Array<ClientProfile>;
  constraints: Array<AppointmentConstraint>;
  createdAt: Scalars['DateTime'];
  id: Scalars['ID'];
  latitude?: Maybe<Scalars['Float']>;
  location: Scalars['String'];
  longitude?: Maybe<Scalars['Float']>;
  marketplace: Marketplace;
  notes?: Maybe<Scalars['String']>;
  procedureBaseDefs: Array<ProcedureBaseDefinition>;
  status: AppointmentRequestStatus;
  updatedAt: Scalars['DateTime'];
};

export type AppointmentRequestFilterInput = {
  client_name?: InputMaybe<FilterStringInput>;
  id?: InputMaybe<FilterIntegerInput>;
  location?: InputMaybe<FilterStringInput>;
  marketplace_id?: InputMaybe<FilterIntegerInput>;
  marketplace_name?: InputMaybe<FilterStringInput>;
  membership?: InputMaybe<FilterStringInput>;
  organization_name?: InputMaybe<FilterStringInput>;
  practitioner_name?: InputMaybe<FilterStringInput>;
  procedure?: InputMaybe<FilterStringInput>;
  status?: InputMaybe<FilterStringInput>;
};

export type AppointmentRequestPage = {
  __typename?: 'AppointmentRequestPage';
  data: Array<AppointmentRequest>;
  totalCount: Scalars['Int'];
};

export type AppointmentRequestPageInput = {
  filter?: InputMaybe<AppointmentRequestFilterInput>;
  limit?: InputMaybe<Scalars['Int']>;
  offset?: InputMaybe<Scalars['Int']>;
  sort?: InputMaybe<Array<AppointmentRequestSortInput>>;
};

export enum AppointmentRequestSortFields {
  Balance = 'BALANCE',
  ClientName = 'CLIENT_NAME',
  Createdat = 'CREATEDAT',
  Id = 'ID',
  Location = 'LOCATION',
  MarketplaceName = 'MARKETPLACE_NAME',
  Membership = 'MEMBERSHIP',
  Procedure = 'PROCEDURE',
  Status = 'STATUS',
  Updatedat = 'UPDATEDAT'
}

export type AppointmentRequestSortInput = {
  direction: SortDirection;
  field: AppointmentRequestSortFields;
};

export enum AppointmentRequestStatus {
  Cancelled = 'CANCELLED',
  Fulfilled = 'FULFILLED',
  Pending = 'PENDING',
  PendingApproval = 'PENDING_APPROVAL'
}

export type AppointmentResponse = {
  __typename?: 'AppointmentResponse';
  createdAt: Scalars['DateTime'];
  /** Time for appointment, or requested new end time */
  end?: Maybe<Scalars['DateTime']>;
  id: Scalars['ID'];
  /** Time for appointment, or requested new start time */
  start?: Maybe<Scalars['DateTime']>;
  status: ParticipationStatus;
  updatedAt: Scalars['DateTime'];
};

export enum AppointmentSortFields {
  Balance = 'BALANCE',
  ClientName = 'CLIENT_NAME',
  Createdat = 'CREATEDAT',
  Duration = 'DURATION',
  Id = 'ID',
  Location = 'LOCATION',
  Orderapproved = 'ORDERAPPROVED',
  PractitionerName = 'PRACTITIONER_NAME',
  Procedure = 'PROCEDURE',
  Start = 'START',
  Status = 'STATUS',
  Updatedat = 'UPDATEDAT'
}

export type AppointmentSortInput = {
  direction: SortDirection;
  field: AppointmentSortFields;
};

export enum AppointmentStatus {
  Booked = 'BOOKED',
  Cancelled = 'CANCELLED',
  Completed = 'COMPLETED',
  Noshow = 'NOSHOW',
  Pending = 'PENDING'
}

export type AppointmentTimeRange = {
  __typename?: 'AppointmentTimeRange';
  end: Scalars['DateTime'];
  id: Scalars['ID'];
  start: Scalars['DateTime'];
};

export type AppointmentTimeRangeInput = {
  end: Scalars['DateTime'];
  start: Scalars['DateTime'];
};

export type ApproveAppointmentRequestInput = {
  appointmentRequestId: Scalars['ID'];
};

export type ArchiveAppointmentInput = {
  appointmentId: Scalars['ID'];
  reason?: InputMaybe<Scalars['String']>;
};

export type ArchiveAppointmentRequestInput = {
  appointmentRequestId: Scalars['ID'];
};

export type ArchiveMarketplaceGroupInput = {
  groupId: Scalars['ID'];
};

export type ArchiveMarketplaceInput = {
  marketplaceId: Scalars['ID'];
};

export type ArchiveOrganizationInput = {
  organizationId: Scalars['ID'];
};

export type ArchivePaymentAccountInput = {
  paymentAccountId: Scalars['ID'];
};

export type ArchiveProfileInput = {
  profileId: Scalars['ID'];
};

export type AssignEmrInstanceInput = {
  emrInstanceId?: InputMaybe<Scalars['ID']>;
  organizationId: Scalars['ID'];
};

export type AssignPaymentAccountInput = {
  marketplaceId?: InputMaybe<Scalars['ID']>;
  organizationId?: InputMaybe<Scalars['ID']>;
  paymentAccountId: Scalars['ID'];
};

export type AssignProcedureProfileInput = {
  procedureProfileId: Scalars['ID'];
  profileId: Scalars['ID'];
};

export type AssignRoleInput = {
  profileId: Scalars['ID'];
  roleId: Scalars['ID'];
};

export type AttentiveIntegration = {
  __typename?: 'AttentiveIntegration';
  apiKeyDescription?: Maybe<Scalars['String']>;
  appointmentBookedEnabled?: Maybe<Scalars['Boolean']>;
  appointmentBookedEvent?: Maybe<Scalars['String']>;
  appointmentCompletedEnabled?: Maybe<Scalars['Boolean']>;
  appointmentCompletedEvent?: Maybe<Scalars['String']>;
  id: Scalars['ID'];
  qualiphyTextsEnabled?: Maybe<Scalars['Boolean']>;
  qualiphyTextsEvent?: Maybe<Scalars['String']>;
};

export type AuthenticateInput = {
  email: Scalars['String'];
  password: Scalars['String'];
};

export type AuthenticatePayload = {
  __typename?: 'AuthenticatePayload';
  accessToken: Scalars['String'];
  refreshToken: Scalars['String'];
  user: User;
};

export type Availability = {
  __typename?: 'Availability';
  createdAt: Scalars['DateTime'];
  /** Availability length in minutes */
  duration: Scalars['Int'];
  end: Scalars['DateTime'];
  id: Scalars['ID'];
  profileId: Scalars['ID'];
  repeat?: Maybe<RepeatRule>;
  start: Scalars['DateTime'];
  type: AvailabilityType;
  tzid: Scalars['String'];
  updatedAt: Scalars['DateTime'];
};

export type AvailabilityCoverage = {
  __typename?: 'AvailabilityCoverage';
  createdAt: Scalars['DateTime'];
  end: Scalars['DateTime'];
  id: Scalars['ID'];
  label: Scalars['String'];
  organizationId: Scalars['ID'];
  profiles: Array<ProcedureProfile>;
  repeat?: Maybe<RepeatRule>;
  start: Scalars['DateTime'];
  threshold: Scalars['Int'];
  tzid: Scalars['String'];
  updatedAt: Scalars['DateTime'];
};

export type AvailabilityCoverageRange = {
  __typename?: 'AvailabilityCoverageRange';
  end: Scalars['DateTime'];
  id: Scalars['ID'];
  label: Scalars['String'];
  procedureProfileIds: Array<Scalars['ID']>;
  start: Scalars['DateTime'];
  type: CoverageType;
  tzid: Scalars['String'];
};

export type AvailabilityCoverageRangeInput = {
  dateRange: Array<Scalars['DateTime']>;
  profileId?: InputMaybe<Scalars['ID']>;
};

export enum AvailabilityType {
  Available = 'AVAILABLE',
  Unavailable = 'UNAVAILABLE'
}

export type CancelAppointmentInput = {
  appointmentId: Scalars['ID'];
  customRefundAmount?: InputMaybe<Scalars['Int']>;
  reason?: InputMaybe<Scalars['String']>;
  refundType?: InputMaybe<AppointmentRefundType>;
};

export type CancelAppointmentRequestInput = {
  appointmentRequestId: Scalars['ID'];
  reason?: InputMaybe<Scalars['String']>;
};

export type ChangeEmailInput = {
  token: Scalars['String'];
};

export type ChartData = {
  __typename?: 'ChartData';
  datasets: Array<Dataset>;
  labels: Array<Scalars['String']>;
};

export type Checkout = {
  __typename?: 'Checkout';
  appointment?: Maybe<Appointment>;
  appointmentRequest?: Maybe<AppointmentRequest>;
  archivedAt?: Maybe<Scalars['DateTime']>;
  balance: Scalars['Int'];
  createdAt: Scalars['DateTime'];
  id: Scalars['ID'];
  items: Array<CheckoutItem>;
  marketplace?: Maybe<Marketplace>;
  marketplaceId?: Maybe<Scalars['ID']>;
  marketplaceUser?: Maybe<MarketplaceUser>;
  marketplaceUserId?: Maybe<Scalars['ID']>;
  paid: Scalars['Int'];
  paymentInstrument?: Maybe<PaymentInstrument>;
  payments: Array<Payment>;
  payouts: Array<Payout>;
  summary?: Maybe<Scalars['String']>;
  total: Scalars['Int'];
  updatedAt: Scalars['DateTime'];
  voidedAt?: Maybe<Scalars['DateTime']>;
};

export type CheckoutFilterInput = {
  balance?: InputMaybe<FilterStringInput>;
  fullName?: InputMaybe<FilterStringInput>;
  id?: InputMaybe<FilterIntegerInput>;
  marketplaceId?: InputMaybe<FilterIntegerInput>;
  paid?: InputMaybe<FilterStringInput>;
  summary?: InputMaybe<FilterStringInput>;
  total?: InputMaybe<FilterStringInput>;
};

export type CheckoutItem = {
  __typename?: 'CheckoutItem';
  createdAt: Scalars['DateTime'];
  description: Scalars['String'];
  id: Scalars['ID'];
  price: Scalars['Int'];
  quantity: Scalars['Int'];
  type: CheckoutItemType;
  updatedAt: Scalars['DateTime'];
};

export type CheckoutItemInput = {
  description?: InputMaybe<Scalars['String']>;
  id?: InputMaybe<Scalars['ID']>;
  price?: InputMaybe<Scalars['Int']>;
  quantity?: InputMaybe<Scalars['Int']>;
  type?: InputMaybe<CheckoutItemType>;
};

export enum CheckoutItemType {
  Discount = 'DISCOUNT',
  Gratuity = 'GRATUITY',
  Membership = 'MEMBERSHIP',
  Other = 'OTHER',
  Package = 'PACKAGE',
  PackageCredit = 'PACKAGE_CREDIT',
  Procedure = 'PROCEDURE',
  PromoCode = 'PROMO_CODE',
  Refund = 'REFUND',
  TravelFee = 'TRAVEL_FEE'
}

export type CheckoutPage = {
  __typename?: 'CheckoutPage';
  data: Array<Checkout>;
  totalCount: Scalars['Int'];
};

export type CheckoutPageInput = {
  filter?: InputMaybe<CheckoutFilterInput>;
  limit?: InputMaybe<Scalars['Int']>;
  offset?: InputMaybe<Scalars['Int']>;
  sort?: InputMaybe<Array<CheckoutSortInput>>;
};

export enum CheckoutSortField {
  Balance = 'BALANCE',
  Createdat = 'CREATEDAT',
  Fullname = 'FULLNAME',
  Id = 'ID',
  Marketplaceid = 'MARKETPLACEID',
  Paid = 'PAID',
  Summary = 'SUMMARY',
  Total = 'TOTAL',
  Updatedat = 'UPDATEDAT'
}

export type CheckoutSortInput = {
  direction: SortDirection;
  field: CheckoutSortField;
};

export type ClientProfile = {
  __typename?: 'ClientProfile';
  address: Scalars['String'];
  appointments: Array<Appointment>;
  archivedAt?: Maybe<Scalars['DateTime']>;
  createdAt: Scalars['DateTime'];
  dob: Scalars['String'];
  email: Scalars['String'];
  familyName: Scalars['String'];
  givenName: Scalars['String'];
  id: Scalars['ID'];
  internalNotes?: Maybe<Scalars['String']>;
  marketplaceUserId?: Maybe<Scalars['ID']>;
  marketplaceUsers: Array<MarketplaceUser>;
  membership: Scalars['String'];
  organizations: Array<Organization>;
  patient?: Maybe<Patient>;
  phone: Scalars['String'];
  sexAssignedAtBirth?: Maybe<Scalars['String']>;
  tzid: Scalars['String'];
  updatedAt: Scalars['DateTime'];
};


export type ClientProfileMarketplaceUsersArgs = {
  groupId?: InputMaybe<Scalars['ID']>;
};

export type ClientProfileFilterInput = {
  address?: InputMaybe<FilterStringInput>;
  email?: InputMaybe<FilterStringInput>;
  familyName?: InputMaybe<FilterStringInput>;
  fullName?: InputMaybe<FilterStringInput>;
  givenName?: InputMaybe<FilterStringInput>;
  id?: InputMaybe<FilterIntegerInput>;
  marketplaceGroup?: InputMaybe<FilterIntegerInput>;
  organization?: InputMaybe<FilterIntegerInput>;
  phone?: InputMaybe<FilterStringInput>;
};

export type ClientProfileInput = {
  address?: InputMaybe<Scalars['String']>;
  /** yyyy-mm-dd */
  dob?: InputMaybe<Scalars['String']>;
  email?: InputMaybe<Scalars['String']>;
  familyName?: InputMaybe<Scalars['String']>;
  givenName?: InputMaybe<Scalars['String']>;
  internalNotes?: InputMaybe<Scalars['String']>;
  phone?: InputMaybe<Scalars['String']>;
  sexAssignedAtBirth?: InputMaybe<Scalars['String']>;
  tzid?: InputMaybe<Scalars['String']>;
};

export type ClientProfilePage = {
  __typename?: 'ClientProfilePage';
  data: Array<ClientProfile>;
  totalCount: Scalars['Int'];
};

export type ClientProfilePageInput = {
  filter?: InputMaybe<ClientProfileFilterInput>;
  limit?: InputMaybe<Scalars['Int']>;
  offset?: InputMaybe<Scalars['Int']>;
  sort?: InputMaybe<Array<ClientProfileSortInput>>;
};

export type ClientProfileRequiredInput = {
  address: Scalars['String'];
  /** yyyy-mm-dd */
  dob: Scalars['String'];
  email: Scalars['String'];
  familyName: Scalars['String'];
  givenName: Scalars['String'];
  internalNotes?: InputMaybe<Scalars['String']>;
  phone: Scalars['String'];
  sexAssignedAtBirth?: InputMaybe<Scalars['String']>;
  tzid: Scalars['String'];
};

export enum ClientProfileSortFields {
  Address = 'ADDRESS',
  Createdat = 'CREATEDAT',
  Dob = 'DOB',
  Email = 'EMAIL',
  Familyname = 'FAMILYNAME',
  Fullname = 'FULLNAME',
  Givenname = 'GIVENNAME',
  Id = 'ID',
  Phone = 'PHONE',
  Updatedat = 'UPDATEDAT'
}

export type ClientProfileSortInput = {
  direction: SortDirection;
  field: ClientProfileSortFields;
};

export type ClinicalProcedure = {
  __typename?: 'ClinicalProcedure';
  createdAt: Scalars['DateTime'];
  id: Scalars['ID'];
  name: Scalars['String'];
  updatedAt: Scalars['DateTime'];
};

export type CompleteAppointmentInput = {
  appointmentId: Scalars['ID'];
  completedAt?: InputMaybe<Scalars['DateTime']>;
  startedAt?: InputMaybe<Scalars['DateTime']>;
};

export type CompleteCheckoutInput = {
  checkoutId: Scalars['ID'];
  expectedAmount?: InputMaybe<Scalars['Int']>;
  isAdminPayment?: InputMaybe<Scalars['Boolean']>;
  paymentMethod?: InputMaybe<PaymentMethodInput>;
};

export type Condition = {
  __typename?: 'Condition';
  createdAt: Scalars['DateTime'];
  id: Scalars['ID'];
  name: Scalars['String'];
  updatedAt: Scalars['DateTime'];
};

export type ConfirmConsumerEmailInput = {
  marketplaceId: Scalars['ID'];
  token: Scalars['String'];
};

export type ConfirmConsumerPhoneInput = {
  code: Scalars['String'];
  marketplaceId: Scalars['ID'];
  marketplaceUserId: Scalars['ID'];
};

export enum CoverageType {
  Covered = 'COVERED',
  NoCoverage = 'NO_COVERAGE',
  UnderCovered = 'UNDER_COVERED'
}

export type CreateAllergyIntoleranceInput = {
  name: Scalars['String'];
  patientId: Scalars['ID'];
};

export type CreateApiKeyInput = {
  name: Scalars['String'];
  roleIds: Array<Scalars['ID']>;
  userId: Scalars['ID'];
};

export type CreateApiKeyPayload = {
  __typename?: 'CreateApiKeyPayload';
  apiKey: ApiKey;
  privateKey: Scalars['String'];
};

export type CreateApplePaySessionInput = {
  displayName: Scalars['String'];
  domain: Scalars['String'];
  validationUrl: Scalars['String'];
};

export type CreateAppointmentCheckoutPayload = {
  __typename?: 'CreateAppointmentCheckoutPayload';
  checkout: Checkout;
  errorMessage?: Maybe<Scalars['String']>;
  success: Scalars['Boolean'];
};

export type CreateAppointmentInput = {
  allowOverlap?: InputMaybe<Scalars['Boolean']>;
  constraintId?: InputMaybe<Scalars['ID']>;
  createdBy?: InputMaybe<Scalars['ID']>;
  end: Scalars['DateTime'];
  location: Scalars['String'];
  notes?: InputMaybe<Scalars['String']>;
  participants: Array<AppointmentParticipantInput>;
  procedureBaseDefIds: Array<Scalars['ID']>;
  start: Scalars['DateTime'];
  status?: InputMaybe<AppointmentStatus>;
};

export type CreateAppointmentRequestInput = {
  clientProfileIds: Array<Scalars['ID']>;
  constraints: Array<AppointmentConstraintInput>;
  gratuity?: InputMaybe<Scalars['Int']>;
  location: Scalars['String'];
  marketplaceId: Scalars['ID'];
  marketplaceUserId?: InputMaybe<Scalars['ID']>;
  notes?: InputMaybe<Scalars['String']>;
  paymentMethod?: InputMaybe<PaymentMethodInput>;
  procedureBaseDefIds: Array<Scalars['ID']>;
  promoCode?: InputMaybe<Scalars['String']>;
};

export type CreateAttachmentInput = {
  filename: Scalars['String'];
  formId?: InputMaybe<Scalars['ID']>;
  label: Scalars['String'];
  patientId?: InputMaybe<Scalars['ID']>;
  /** document file token */
  token: Scalars['String'];
};

export type CreateAvailabilityCoverageInput = {
  end: Scalars['DateTime'];
  label: Scalars['String'];
  organizationId: Scalars['ID'];
  procedureProfileIds: Array<Scalars['ID']>;
  repeat?: InputMaybe<RepeatRuleInput>;
  start: Scalars['DateTime'];
  threshold: Scalars['Int'];
  tzid: Scalars['String'];
};

export type CreateAvailabilityInput = {
  end: Scalars['DateTime'];
  profileId: Scalars['ID'];
  repeat?: InputMaybe<RepeatRuleInput>;
  start: Scalars['DateTime'];
  type: AvailabilityType;
};

export type CreateClientProfileInput = {
  address: Scalars['String'];
  /** yyyy-mm-dd */
  dob: Scalars['String'];
  email: Scalars['String'];
  familyName: Scalars['String'];
  givenName: Scalars['String'];
  internalNotes?: InputMaybe<Scalars['String']>;
  organizationId: Scalars['ID'];
  phone: Scalars['String'];
  sexAssignedAtBirth?: InputMaybe<Scalars['String']>;
  tzid: Scalars['String'];
};

export type CreateClinicalProcedureInput = {
  name: Scalars['String'];
  patientId: Scalars['ID'];
};

export type CreateConditionInput = {
  name: Scalars['String'];
  patientId: Scalars['ID'];
};

export type CreateDiscountInput = {
  membershipDefinitionId: Scalars['ID'];
  percentage: Scalars['Int'];
  procedureGroupIds: Array<Scalars['String']>;
};

export type CreateDocumentInput = {
  emrInstanceId: Scalars['ID'];
  filename: Scalars['String'];
  label?: InputMaybe<Scalars['String']>;
  /** document file token */
  token: Scalars['String'];
  type: DocumentType;
};

export type CreateEmrInstanceInput = {
  label: Scalars['String'];
};

export type CreateFormNoteInput = {
  formId: Scalars['ID'];
  note: Scalars['String'];
  profileId: Scalars['ID'];
};

export type CreateFormTemplateInput = {
  emrInstanceId: Scalars['ID'];
  items: Array<FormTemplateItemInput>;
  name: Scalars['String'];
  parentId?: InputMaybe<Scalars['ID']>;
  type: FormType;
};

export type CreateGeoperimeterInput = {
  lat?: InputMaybe<Scalars['Float']>;
  lng?: InputMaybe<Scalars['Float']>;
  organizationId: Scalars['ID'];
  paths?: InputMaybe<Scalars['String']>;
  radius?: InputMaybe<Scalars['Float']>;
  travelFee?: InputMaybe<Scalars['Int']>;
  type: GeoperimeterType;
};

export type CreateImmunizationInput = {
  name: Scalars['String'];
  patientId: Scalars['ID'];
};

export type CreateLabInput = {
  name: Scalars['String'];
  patientId: Scalars['ID'];
};

export type CreateMarketplaceInput = {
  faviconToken?: InputMaybe<Scalars['String']>;
  feeProfileBasisPoints?: InputMaybe<Scalars['Int']>;
  feeProfileFixed?: InputMaybe<Scalars['Int']>;
  groupId?: InputMaybe<Scalars['ID']>;
  hasPaymentPolicy?: InputMaybe<Scalars['Boolean']>;
  logoToken?: InputMaybe<Scalars['String']>;
  name: Scalars['String'];
  organizationIds?: InputMaybe<Array<Scalars['ID']>>;
  ownerProfileIds?: InputMaybe<Array<Scalars['ID']>>;
  paymentCollectionMethod?: InputMaybe<PaymentCollectionMethod>;
  paymentDepositType?: InputMaybe<PaymentDepositType>;
  paymentDepositValue?: InputMaybe<Scalars['Float']>;
  paymentPolicyName?: InputMaybe<Scalars['String']>;
  paymentPolicyText?: InputMaybe<Scalars['String']>;
  primaryColor?: InputMaybe<Scalars['String']>;
  requireDispatchApproval?: InputMaybe<Scalars['Boolean']>;
  requirePaymentPolicyAttestation?: InputMaybe<Scalars['Boolean']>;
  requirePractitionerApproval?: InputMaybe<Scalars['Boolean']>;
  reviewsIoKey?: InputMaybe<Scalars['String']>;
  reviewsIoStoreId?: InputMaybe<Scalars['String']>;
  slackWebhookUrl?: InputMaybe<Scalars['String']>;
};

export type CreateMarketplaceUserInput = {
  clientProfile: ClientProfileRequiredInput;
  email?: InputMaybe<Scalars['String']>;
  emailConfirmed?: InputMaybe<Scalars['Boolean']>;
  emailOptIn?: InputMaybe<Scalars['Boolean']>;
  groupId: Scalars['ID'];
  phone?: InputMaybe<Scalars['String']>;
  phoneConfirmed?: InputMaybe<Scalars['Boolean']>;
  phoneOptIn?: InputMaybe<Scalars['Boolean']>;
};

export type CreateMedicationInput = {
  name: Scalars['String'];
  organizationId: Scalars['ID'];
  unit: Scalars['String'];
};

export type CreateMedicationStatementInput = {
  name: Scalars['String'];
  patientId: Scalars['ID'];
};

export type CreateMembershipDefinitionInput = {
  advertise: Scalars['Boolean'];
  description?: InputMaybe<Scalars['String']>;
  list?: InputMaybe<Scalars['String']>;
  marketplaceId: Scalars['ID'];
  name: Scalars['String'];
  packageId?: InputMaybe<Scalars['ID']>;
  period: MembershipDefinitionPeriodType;
  price: Scalars['Int'];
};

export type CreateMembershipInput = {
  marketplaceUserId: Scalars['ID'];
  membershipDefinitionId: Scalars['ID'];
  paymentInstrumentId?: InputMaybe<Scalars['ID']>;
  start?: InputMaybe<Scalars['DateTime']>;
  status: MembershipStatusType;
};

export type CreateObservationsInput = {
  createdById: Scalars['ID'];
  formId: Scalars['ID'];
  observations: Array<ObservationInput>;
};

export type CreateOrderInput = {
  expiresAt: Scalars['DateTime'];
  note?: InputMaybe<Scalars['String']>;
  patientId: Scalars['ID'];
  procedureDefIds?: InputMaybe<Array<Scalars['ID']>>;
  profileId: Scalars['ID'];
  refills: Scalars['Int'];
  startsAt?: InputMaybe<Scalars['DateTime']>;
};

export type CreateOrganizationInput = {
  address?: InputMaybe<Scalars['String']>;
  email?: InputMaybe<Scalars['String']>;
  emrInstanceId?: InputMaybe<Scalars['ID']>;
  enablePractitionerSms?: InputMaybe<Scalars['Boolean']>;
  enableReceiptSending?: InputMaybe<Scalars['Boolean']>;
  googleReviewsUrl?: InputMaybe<Scalars['String']>;
  name: Scalars['String'];
  ownerEmails: Array<Scalars['String']>;
  phone?: InputMaybe<Scalars['String']>;
  providesAtClinic?: InputMaybe<Scalars['Boolean']>;
  qualiphyApiKey?: InputMaybe<Scalars['String']>;
  slackWebhookUrl?: InputMaybe<Scalars['String']>;
  state?: InputMaybe<StateCode>;
  tzid: Scalars['String'];
};

export type CreatePackageInput = {
  advertise: Scalars['Boolean'];
  description?: InputMaybe<Scalars['String']>;
  list?: InputMaybe<Scalars['String']>;
  marketplaceId: Scalars['ID'];
  name: Scalars['String'];
  price: Scalars['Int'];
};

export type CreatePackageItemDefinitionInput = {
  packageId: Scalars['ID'];
  points: Scalars['Int'];
  procedureGroupIds: Array<Scalars['ID']>;
};

export type CreatePatientInput = {
  clientProfileId: Scalars['ID'];
};

export type CreatePaymentAccountInput = {
  enabled?: InputMaybe<Scalars['Boolean']>;
  label: Scalars['String'];
  marketplaceId?: InputMaybe<Scalars['ID']>;
  organizationId?: InputMaybe<Scalars['ID']>;
};

export type CreateProcedureBaseDefinitionGroupInput = {
  bannerToken?: InputMaybe<Scalars['String']>;
  bgcolor?: InputMaybe<Scalars['String']>;
  description?: InputMaybe<Scalars['String']>;
  fontColor?: InputMaybe<Scalars['String']>;
  marketplaceId: Scalars['ID'];
  name: Scalars['String'];
  thumbnailToken?: InputMaybe<Scalars['String']>;
  type: ProcedureGroupType;
};

export type CreateProcedureBaseDefinitionInput = {
  category?: InputMaybe<Scalars['String']>;
  description: Scalars['String'];
  duration: Scalars['Float'];
  ingredients?: InputMaybe<Scalars['String']>;
  marketplaceId: Scalars['ID'];
  name: Scalars['String'];
  points?: InputMaybe<Scalars['Float']>;
  tagline?: InputMaybe<Scalars['String']>;
  thumbnailToken?: InputMaybe<Scalars['String']>;
};

export type CreateProcedureDefinitionInput = {
  assessmentFormTemplateId?: InputMaybe<Scalars['ID']>;
  baseDefinitionIds?: InputMaybe<Array<Scalars['ID']>>;
  consentFormIds?: InputMaybe<Array<Scalars['ID']>>;
  description: Scalars['String'];
  duration: Scalars['Float'];
  interventionFormTemplateId?: InputMaybe<Scalars['ID']>;
  medicationProtocols?: InputMaybe<Array<MedicationProtocolInput>>;
  name: Scalars['String'];
  organizationId: Scalars['ID'];
  price: Scalars['Float'];
};

export type CreateProcedureProfileInput = {
  name: Scalars['String'];
  organizationId: Scalars['ID'];
  procedureDefIds?: InputMaybe<Array<Scalars['ID']>>;
};

export type CreateProfileInput = {
  address?: InputMaybe<Scalars['String']>;
  allowSmsNotifications?: InputMaybe<Scalars['Boolean']>;
  availableUntil?: InputMaybe<Scalars['DateTime']>;
  color?: InputMaybe<Scalars['String']>;
  email?: InputMaybe<Scalars['String']>;
  familyName?: InputMaybe<Scalars['String']>;
  givenName?: InputMaybe<Scalars['String']>;
  organizationId: Scalars['ID'];
  phone?: InputMaybe<Scalars['String']>;
  roleId: Scalars['ID'];
  sendInvitation?: InputMaybe<Scalars['Boolean']>;
  title?: InputMaybe<Scalars['String']>;
  tzid?: InputMaybe<Scalars['String']>;
};

export type CreatePromoCodeInput = {
  activationEndDate: Scalars['String'];
  activationStartDate: Scalars['String'];
  active?: InputMaybe<Scalars['Boolean']>;
  code?: InputMaybe<Scalars['String']>;
  discountType: PromoCodeDiscountType;
  discountValue: Scalars['Int'];
  marketplaceId: Scalars['ID'];
  minimumBookingAmount?: InputMaybe<Scalars['Int']>;
  name: Scalars['String'];
  organizationIds?: InputMaybe<Array<Scalars['String']>>;
  procedureGroupIds?: InputMaybe<Array<Scalars['String']>>;
  usageEndDate: Scalars['String'];
  usageLimitPerUser?: InputMaybe<Scalars['Int']>;
  usageLimitTotal?: InputMaybe<Scalars['Int']>;
  usageStartDate: Scalars['String'];
};

export type CreateRoleInput = {
  name: Scalars['String'];
  permissions: Array<Scalars['String']>;
  resourceId?: InputMaybe<Scalars['ID']>;
  scope: RoleScope;
};

export type CreateUploadUrlInput = {
  contentType: Scalars['String'];
};

export type CreateUploadUrlPayload = {
  __typename?: 'CreateUploadUrlPayload';
  /** Inputs to include with the form data */
  fields: Scalars['String'];
  /** A token used to later attach the file to a document */
  token: Scalars['String'];
  /** Send an HTTP POST request to this URL, with the file and form fields encoded as multipart/form-data */
  url: Scalars['String'];
};

export type CreateUserInput = {
  email: Scalars['String'];
};

export type CreateVitalsInput = {
  createdById: Scalars['ID'];
  patientId: Scalars['ID'];
  recordedAt: Scalars['DateTime'];
  vitals: Array<VitalSignInput>;
};

export type Dataset = {
  __typename?: 'Dataset';
  backgroundColor: Array<Scalars['String']>;
  borderColor: Array<Scalars['String']>;
  data: Array<Scalars['Int']>;
  label: Scalars['String'];
};

export type DeclineAppointmentInput = {
  appointmentId: Scalars['ID'];
  profileId: Scalars['ID'];
};

export type Discount = {
  __typename?: 'Discount';
  archivedAt?: Maybe<Scalars['DateTime']>;
  createdAt: Scalars['DateTime'];
  id: Scalars['ID'];
  membershipDefinitionId: Scalars['ID'];
  percentage: Scalars['Int'];
  procedureGroups: Array<ProcedureBaseDefinitionGroup>;
  updatedAt: Scalars['DateTime'];
};

export type Document = {
  __typename?: 'Document';
  archivedAt?: Maybe<Scalars['DateTime']>;
  createdAt: Scalars['DateTime'];
  fileSize: Scalars['Int'];
  filename: Scalars['String'];
  id: Scalars['ID'];
  label: Scalars['String'];
  type: DocumentType;
  updatedAt: Scalars['DateTime'];
  url: Scalars['String'];
  version: Scalars['Int'];
};

export type DocumentSignature = {
  __typename?: 'DocumentSignature';
  archivedAt?: Maybe<Scalars['DateTime']>;
  createdAt: Scalars['DateTime'];
  documentId: Scalars['ID'];
  documentLabel: Scalars['String'];
  id: Scalars['ID'];
  latitude?: Maybe<Scalars['Float']>;
  longitude?: Maybe<Scalars['Float']>;
  signedAt: Scalars['DateTime'];
  signerEmail?: Maybe<Scalars['String']>;
  signerName: Scalars['String'];
  signerType: DocumentSignatureSignerType;
  tzid: Scalars['String'];
  updatedAt: Scalars['DateTime'];
  url?: Maybe<Scalars['String']>;
  verificationDetail?: Maybe<Scalars['String']>;
  verificationType: DocumentSignatureVerificationType;
  version: Scalars['Int'];
};

export enum DocumentSignatureSignerType {
  Patient = 'PATIENT'
}

export enum DocumentSignatureVerificationType {
  Email = 'EMAIL',
  Witness = 'WITNESS'
}

export enum DocumentType {
  Attachment = 'ATTACHMENT',
  ChartForm = 'CHART_FORM',
  ConsentForm = 'CONSENT_FORM'
}

export type EmrInstance = {
  __typename?: 'EmrInstance';
  archivedAt?: Maybe<Scalars['DateTime']>;
  consentForms: Array<Document>;
  createdAt: Scalars['DateTime'];
  formTemplates: Array<FormTemplate>;
  id: Scalars['ID'];
  label: Scalars['String'];
  organizations: Array<Organization>;
  updatedAt: Scalars['DateTime'];
};

export type FcmTokenInput = {
  token: Scalars['String'];
};

export type FilterDateInput = {
  between?: InputMaybe<Array<Scalars['DateTime']>>;
  gte?: InputMaybe<Scalars['DateTime']>;
  lte?: InputMaybe<Scalars['DateTime']>;
};

export type FilterIntegerInput = {
  between?: InputMaybe<Array<Scalars['Int']>>;
  eq?: InputMaybe<Scalars['Int']>;
  gt?: InputMaybe<Scalars['Int']>;
  gte?: InputMaybe<Scalars['Int']>;
  lt?: InputMaybe<Scalars['Int']>;
  lte?: InputMaybe<Scalars['Int']>;
  notBetween?: InputMaybe<Array<Scalars['Int']>>;
  oneOf?: InputMaybe<Array<Scalars['Int']>>;
};

export type FilterStringInput = {
  contains?: InputMaybe<Array<Scalars['String']>>;
  containsAll?: InputMaybe<Array<Scalars['String']>>;
  exact?: InputMaybe<Array<Scalars['String']>>;
};

export type Form = {
  __typename?: 'Form';
  archivedAt?: Maybe<Scalars['DateTime']>;
  attachments?: Maybe<Array<Document>>;
  createdAt: Scalars['DateTime'];
  document?: Maybe<Document>;
  editable: Scalars['Boolean'];
  id: Scalars['ID'];
  items: Array<FormItem>;
  lockedAt?: Maybe<Scalars['DateTime']>;
  lockedBy?: Maybe<Profile>;
  lockedById?: Maybe<Scalars['ID']>;
  notes: Array<FormNote>;
  observations: Array<Observation>;
  patientId: Scalars['ID'];
  providers: Array<Profile>;
  status: FormStatusType;
  type: FormType;
  updatedAt: Scalars['DateTime'];
};

export type FormItem = {
  __typename?: 'FormItem';
  code: Scalars['String'];
  createdAt: Scalars['DateTime'];
  id: Scalars['ID'];
  index: Scalars['Int'];
  label: Scalars['String'];
  options?: Maybe<Scalars['String']>;
  type: FormItemType;
  updatedAt: Scalars['DateTime'];
};

export enum FormItemType {
  Boolean = 'BOOLEAN',
  Datetime = 'DATETIME',
  Header = 'HEADER',
  Multiselect = 'MULTISELECT',
  Select = 'SELECT',
  Text = 'TEXT'
}

export type FormNote = {
  __typename?: 'FormNote';
  archivedAt?: Maybe<Scalars['DateTime']>;
  author: Scalars['String'];
  createdAt: Scalars['DateTime'];
  edited: Scalars['Boolean'];
  id: Scalars['ID'];
  note: Scalars['String'];
  profileId: Scalars['ID'];
  updatedAt: Scalars['DateTime'];
};

export enum FormStatusType {
  Created = 'CREATED',
  Locked = 'LOCKED',
  Unlocked = 'UNLOCKED'
}

export type FormTemplate = {
  __typename?: 'FormTemplate';
  createdAt: Scalars['DateTime'];
  id: Scalars['ID'];
  items: Array<FormTemplateItem>;
  name: Scalars['String'];
  parentId?: Maybe<Scalars['ID']>;
  type: FormType;
  updatedAt: Scalars['DateTime'];
};

export type FormTemplateItem = {
  __typename?: 'FormTemplateItem';
  createdAt: Scalars['DateTime'];
  id: Scalars['ID'];
  index: Scalars['Int'];
  label: Scalars['String'];
  options?: Maybe<Scalars['String']>;
  type: FormItemType;
  updatedAt: Scalars['DateTime'];
};

export type FormTemplateItemInput = {
  id?: InputMaybe<Scalars['ID']>;
  label: Scalars['String'];
  options?: InputMaybe<Scalars['String']>;
  type: FormItemType;
};

export enum FormType {
  Assessment = 'ASSESSMENT',
  Intervention = 'INTERVENTION'
}

export type Geoperimeter = {
  __typename?: 'Geoperimeter';
  createdAt: Scalars['DateTime'];
  id: Scalars['ID'];
  lat?: Maybe<Scalars['Float']>;
  lng?: Maybe<Scalars['Float']>;
  organizationId: Scalars['ID'];
  paths?: Maybe<Scalars['String']>;
  radius?: Maybe<Scalars['Float']>;
  travelFee?: Maybe<Scalars['Int']>;
  type: GeoperimeterType;
  updatedAt: Scalars['DateTime'];
};

export enum GeoperimeterType {
  Circle = 'CIRCLE',
  Polygon = 'POLYGON'
}

export type GrantPackageInput = {
  expiresAt?: InputMaybe<Scalars['DateTime']>;
  marketplaceUserId: Scalars['ID'];
  packageId: Scalars['ID'];
  paymentMethod?: InputMaybe<PaymentMethodInput>;
  primaryOrganizationId?: InputMaybe<Scalars['ID']>;
};

export type Immunization = {
  __typename?: 'Immunization';
  createdAt: Scalars['DateTime'];
  id: Scalars['ID'];
  name: Scalars['String'];
  updatedAt: Scalars['DateTime'];
};

export type JoinOrganizationInput = {
  code: Scalars['String'];
};

export type Lab = {
  __typename?: 'Lab';
  createdAt: Scalars['DateTime'];
  id: Scalars['ID'];
  name: Scalars['String'];
  updatedAt: Scalars['DateTime'];
};

export type LeaveOrganizationInput = {
  organizationId: Scalars['ID'];
  userId?: InputMaybe<Scalars['ID']>;
};

export type LockFormInput = {
  formId: Scalars['ID'];
  lockedById: Scalars['ID'];
};

export type Marketplace = {
  __typename?: 'Marketplace';
  appointmentRequests: AppointmentRequestPage;
  archivedAt?: Maybe<Scalars['DateTime']>;
  attentive?: Maybe<AttentiveIntegration>;
  createdAt: Scalars['DateTime'];
  favicon?: Maybe<Scalars['String']>;
  feeProfileBasisPoints?: Maybe<Scalars['Int']>;
  feeProfileFixed?: Maybe<Scalars['Int']>;
  group: MarketplaceGroup;
  groupId: Scalars['ID'];
  hasPaymentPolicy?: Maybe<Scalars['Boolean']>;
  id: Scalars['ID'];
  logo?: Maybe<Scalars['String']>;
  membershipDefinitions: Array<MembershipDefinition>;
  name: Scalars['String'];
  navigationGroupId?: Maybe<Scalars['ID']>;
  organizations: Array<Organization>;
  packages: Array<Package>;
  paymentAccountId?: Maybe<Scalars['ID']>;
  paymentAccounts: Array<PaymentAccount>;
  paymentCollectionMethod?: Maybe<PaymentCollectionMethod>;
  paymentDepositType?: Maybe<PaymentDepositType>;
  paymentDepositValue?: Maybe<Scalars['Float']>;
  paymentPolicyName?: Maybe<Scalars['String']>;
  paymentPolicyText?: Maybe<Scalars['String']>;
  primaryColor?: Maybe<Scalars['String']>;
  procedureBaseDefGroups: Array<ProcedureBaseDefinitionGroup>;
  procedureBaseDefs: Array<ProcedureBaseDefinition>;
  requireDispatchApproval: Scalars['Boolean'];
  requirePaymentPolicyAttestation?: Maybe<Scalars['Boolean']>;
  requirePractitionerApproval: Scalars['Boolean'];
  reviewsIoKeyDescription?: Maybe<Scalars['String']>;
  reviewsIoStoreId?: Maybe<Scalars['String']>;
  roles: Array<Role>;
  segment?: Maybe<SegmentIntegration>;
  sendgrid?: Maybe<SendgridIntegration>;
  slackWebhookUrl?: Maybe<Scalars['String']>;
  twilio?: Maybe<TwilioIntegration>;
  updatedAt: Scalars['DateTime'];
};


export type MarketplaceAppointmentRequestsArgs = {
  page?: InputMaybe<AppointmentRequestPageInput>;
};

export type MarketplaceGroup = {
  __typename?: 'MarketplaceGroup';
  archivedAt?: Maybe<Scalars['DateTime']>;
  createdAt: Scalars['DateTime'];
  id: Scalars['ID'];
  label: Scalars['String'];
  marketplaceUsers: MarketplaceUserPage;
  marketplaces: Array<Marketplace>;
  updatedAt: Scalars['DateTime'];
};


export type MarketplaceGroupMarketplaceUsersArgs = {
  page?: InputMaybe<MarketplaceUserPageInput>;
};

export type MarketplaceUser = {
  __typename?: 'MarketplaceUser';
  appointmentRequests: Array<AppointmentRequest>;
  appointments: Array<Appointment>;
  archivedAt?: Maybe<Scalars['DateTime']>;
  checkouts: Array<Checkout>;
  clientProfile: ClientProfile;
  clientProfileId: Scalars['ID'];
  clientProfiles: Array<ClientProfile>;
  createdAt: Scalars['DateTime'];
  email?: Maybe<Scalars['String']>;
  emailConfirmed: Scalars['Boolean'];
  emailOptIn: Scalars['Boolean'];
  groupId: Scalars['ID'];
  id: Scalars['ID'];
  marketplaceGroup: MarketplaceGroup;
  marketplaceIds: Array<Scalars['ID']>;
  membership: Scalars['String'];
  memberships: Array<Membership>;
  packageItems: Array<PackageItem>;
  paymentInstruments: Array<PaymentInstrument>;
  phone?: Maybe<Scalars['String']>;
  phoneConfirmed: Scalars['Boolean'];
  phoneOptIn: Scalars['Boolean'];
  primaryInstrumentId?: Maybe<Scalars['ID']>;
  updatedAt: Scalars['DateTime'];
};

export type MarketplaceUserFilterInput = {
  address?: InputMaybe<FilterStringInput>;
  clientProfileId?: InputMaybe<FilterIntegerInput>;
  email?: InputMaybe<FilterStringInput>;
  familyName?: InputMaybe<FilterStringInput>;
  fullName?: InputMaybe<FilterStringInput>;
  givenName?: InputMaybe<FilterStringInput>;
  groupId?: InputMaybe<FilterIntegerInput>;
  id?: InputMaybe<FilterIntegerInput>;
  membership?: InputMaybe<FilterStringInput>;
  memberships?: InputMaybe<FilterStringInput>;
  phone?: InputMaybe<FilterStringInput>;
};

export type MarketplaceUserPage = {
  __typename?: 'MarketplaceUserPage';
  data: Array<MarketplaceUser>;
  totalCount: Scalars['Int'];
};

export type MarketplaceUserPageInput = {
  filter?: InputMaybe<MarketplaceUserFilterInput>;
  limit?: InputMaybe<Scalars['Int']>;
  offset?: InputMaybe<Scalars['Int']>;
  sort?: InputMaybe<Array<MarketplaceUserSortInput>>;
};

export enum MarketplaceUserSortFields {
  Address = 'ADDRESS',
  Clientprofileid = 'CLIENTPROFILEID',
  Createdat = 'CREATEDAT',
  Dob = 'DOB',
  Email = 'EMAIL',
  Familyname = 'FAMILYNAME',
  Fullname = 'FULLNAME',
  Givenname = 'GIVENNAME',
  Id = 'ID',
  Membership = 'MEMBERSHIP',
  Memberships = 'MEMBERSHIPS',
  Phone = 'PHONE',
  Updatedat = 'UPDATEDAT'
}

export type MarketplaceUserSortInput = {
  direction: SortDirection;
  field: MarketplaceUserSortFields;
};

export type Medication = {
  __typename?: 'Medication';
  archivedAt?: Maybe<Scalars['DateTime']>;
  createdAt: Scalars['DateTime'];
  id: Scalars['ID'];
  name: Scalars['String'];
  organizationId: Scalars['ID'];
  unit: Scalars['String'];
  updatedAt: Scalars['DateTime'];
};

export type MedicationProtocol = {
  __typename?: 'MedicationProtocol';
  createdAt: Scalars['DateTime'];
  dose: Scalars['Float'];
  id: Scalars['ID'];
  medication: Medication;
  updatedAt: Scalars['DateTime'];
};

export type MedicationProtocolInput = {
  dose: Scalars['Float'];
  medicationId: Scalars['ID'];
};

export type MedicationStatement = {
  __typename?: 'MedicationStatement';
  createdAt: Scalars['DateTime'];
  id: Scalars['ID'];
  name: Scalars['String'];
  updatedAt: Scalars['DateTime'];
};

export type Membership = {
  __typename?: 'Membership';
  id: Scalars['ID'];
  marketplaceName: Scalars['String'];
  marketplaceUserId: Scalars['ID'];
  membershipDefinitionId: Scalars['ID'];
  name: Scalars['String'];
  renewalDate: Scalars['DateTime'];
  status: MembershipStatusType;
};

export type MembershipDefinition = {
  __typename?: 'MembershipDefinition';
  advertise: Scalars['Boolean'];
  archivedAt?: Maybe<Scalars['DateTime']>;
  createdAt: Scalars['DateTime'];
  description?: Maybe<Scalars['String']>;
  discounts: Array<Discount>;
  id: Scalars['ID'];
  list?: Maybe<Scalars['String']>;
  marketplaceId: Scalars['ID'];
  name: Scalars['String'];
  packageId?: Maybe<Scalars['ID']>;
  period: MembershipDefinitionPeriodType;
  price: Scalars['Int'];
  updatedAt: Scalars['DateTime'];
};

export enum MembershipDefinitionPeriodType {
  Monthly = 'MONTHLY',
  Quarterly = 'QUARTERLY',
  Yearly = 'YEARLY'
}

export enum MembershipStatusType {
  Active = 'ACTIVE',
  Cancelled = 'CANCELLED',
  Inactive = 'INACTIVE',
  PaymentError = 'PAYMENT_ERROR'
}

export enum MetricChartTypes {
  AppointmentRequestsByMarketplace = 'AppointmentRequestsByMarketplace',
  AppointmentRequestsByStatus = 'AppointmentRequestsByStatus',
  AppointmentsByLocation = 'AppointmentsByLocation',
  AppointmentsByLocationTrend = 'AppointmentsByLocationTrend',
  AppointmentsByProcedure = 'AppointmentsByProcedure',
  AppointmentsByProcedureTrend = 'AppointmentsByProcedureTrend',
  AppointmentsByStatus = 'AppointmentsByStatus',
  Memberships = 'Memberships',
  OrganizationCount = 'OrganizationCount',
  UserCount = 'UserCount'
}

export type MetricData = {
  __typename?: 'MetricData';
  count?: Maybe<Scalars['Int']>;
  data?: Maybe<ChartData>;
  id: Scalars['ID'];
  type: MetricChartTypes;
};

export type MetricsInput = {
  charts: Array<MetricChartTypes>;
  dateRange: Array<Scalars['DateTime']>;
  marketplaceIds: Array<Scalars['ID']>;
  organizationIds: Array<Scalars['ID']>;
  procedureBaseDefIds: Array<Scalars['ID']>;
  tzid?: InputMaybe<Scalars['String']>;
};

export type Mutation = {
  __typename?: 'Mutation';
  acceptAppointment?: Maybe<Appointment>;
  addOrganizationsToMarketplace: Scalars['Boolean'];
  approveAppointmentRequest?: Maybe<AppointmentRequest>;
  archiveAppointment?: Maybe<Appointment>;
  archiveAppointmentRequest?: Maybe<AppointmentRequest>;
  archiveDiscount?: Maybe<Scalars['Boolean']>;
  archiveDocument: Scalars['Boolean'];
  archiveEmrInstance: Scalars['Boolean'];
  archiveFormNote: Scalars['Boolean'];
  archiveMarketplace: Scalars['Boolean'];
  archiveMarketplaceGroup: Scalars['Boolean'];
  archiveMembershipDefinition?: Maybe<Scalars['Boolean']>;
  archiveOrganization: Scalars['Boolean'];
  archivePackage?: Maybe<Scalars['Boolean']>;
  archivePackageItemDefinition?: Maybe<Scalars['Boolean']>;
  archivePaymentAccount: Scalars['Boolean'];
  archiveProfile: Scalars['Boolean'];
  archivePromoCode: Scalars['Boolean'];
  assignEmrInstance: Scalars['Boolean'];
  assignPaymentAccount: Scalars['Boolean'];
  assignProcedureProfile: Scalars['Boolean'];
  assignRole: Scalars['Boolean'];
  authenticate: AuthenticatePayload;
  authenticateAs: AuthenticatePayload;
  cancelAppointment?: Maybe<Appointment>;
  cancelAppointmentRequest?: Maybe<AppointmentRequest>;
  cancelMembership?: Maybe<Scalars['Boolean']>;
  changeEmail: Scalars['Boolean'];
  completeAppointment?: Maybe<Appointment>;
  completeCheckout: CreateAppointmentCheckoutPayload;
  confirmConsumerEmail: Scalars['Boolean'];
  confirmConsumerPhone: Scalars['Boolean'];
  createAllergyIntolerance: AllergyIntolerance;
  createApiKey: CreateApiKeyPayload;
  createApplePayInstrument?: Maybe<PaymentInstrument>;
  createApplePaySession?: Maybe<Scalars['String']>;
  createAppointment?: Maybe<Appointment>;
  createAppointmentRequest?: Maybe<AppointmentRequest>;
  createAttachment: Document;
  createAvailability?: Maybe<Availability>;
  createAvailabilityCoverage?: Maybe<AvailabilityCoverage>;
  createClientProfile?: Maybe<ClientProfile>;
  createClinicalProcedure: ClinicalProcedure;
  createCondition: Condition;
  createDiscount?: Maybe<Discount>;
  createDocument: Document;
  createEmrInstance?: Maybe<EmrInstance>;
  createFormNote?: Maybe<FormNote>;
  createFormTemplate?: Maybe<FormTemplate>;
  createGeoperimeter?: Maybe<Geoperimeter>;
  createImmunization: Immunization;
  createLab: Lab;
  createMarketplace?: Maybe<Marketplace>;
  createMarketplaceUser?: Maybe<MarketplaceUser>;
  createMedication: Medication;
  createMedicationStatement: MedicationStatement;
  createMembership?: Maybe<Membership>;
  createMembershipDefinition?: Maybe<MembershipDefinition>;
  createObservations: Form;
  createOrder: Order;
  createOrganization?: Maybe<Organization>;
  createPackage?: Maybe<Package>;
  createPackageItemDefinition?: Maybe<PackageItemDefinition>;
  createPatient: Patient;
  createPaymentAccount?: Maybe<PaymentAccount>;
  createPaymentInstrument?: Maybe<PaymentInstrument>;
  createProcedureBaseDefinition?: Maybe<ProcedureBaseDefinition>;
  createProcedureBaseDefinitionGroup?: Maybe<ProcedureBaseDefinitionGroup>;
  createProcedureBaseDefinitionTag: ProcedureBaseDefinition;
  createProcedureDefinition?: Maybe<ProcedureDefinition>;
  createProcedureProfile?: Maybe<ProcedureProfile>;
  createProfile: Profile;
  createPromoCode?: Maybe<PromoCode>;
  createRole?: Maybe<Role>;
  createUploadUrl: CreateUploadUrlPayload;
  createUser: User;
  createVitals: Patient;
  declineAppointment?: Maybe<Appointment>;
  deleteAllergyIntolerance: Patient;
  deleteAvailability: Scalars['Boolean'];
  deleteAvailabilityCoverage?: Maybe<Scalars['Boolean']>;
  deleteClinicalProcedure: Patient;
  deleteCondition: Patient;
  deleteFormTemplate: Scalars['Boolean'];
  deleteGeoperimeter?: Maybe<Scalars['Boolean']>;
  deleteImmunization: Patient;
  deleteLab: Patient;
  deleteMedication: Scalars['Boolean'];
  deleteMedicationStatement: Patient;
  deleteMembership?: Maybe<Scalars['Boolean']>;
  deleteObservation: Form;
  deleteOrder: Scalars['Boolean'];
  deletePackageItem?: Maybe<Scalars['Boolean']>;
  deletePaymentInstrument?: Maybe<Scalars['Boolean']>;
  deleteProcedureBaseDefinition: Scalars['Boolean'];
  deleteProcedureBaseDefinitionGroup?: Maybe<Scalars['Boolean']>;
  deleteProcedureBaseDefinitionTag: ProcedureBaseDefinition;
  deleteProcedureDefinition: Scalars['Boolean'];
  deleteProcedureProfile: Scalars['Boolean'];
  deleteQualiphyExam: Scalars['Boolean'];
  deleteReport: Scalars['Boolean'];
  deleteRole: Scalars['Boolean'];
  deleteVital: Patient;
  fcmToken: Scalars['Boolean'];
  grantPackage?: Maybe<Array<PackageItem>>;
  joinOrganization?: Maybe<Profile>;
  leaveOrganization: Scalars['Boolean'];
  lockForm: Form;
  logout: Scalars['Boolean'];
  reactivateMembership?: Maybe<Membership>;
  refreshTokens: AuthenticatePayload;
  refundCheckout: Scalars['Boolean'];
  register: AuthenticatePayload;
  removeOrganizationsFromMarketplace: Scalars['Boolean'];
  reportAppointments: Report;
  reportPersonnel: Report;
  requestChangeEmail: Scalars['Boolean'];
  requestConfirmConsumerEmail: Scalars['Boolean'];
  requestConfirmConsumerPhone: Scalars['Boolean'];
  requestResetPassword: Scalars['Boolean'];
  requestVerifyEmail: Scalars['Boolean'];
  resendInvitationCode: Scalars['Boolean'];
  resendQualiphyExamInvite: QualiphyInvitation;
  resetAppointment?: Maybe<Appointment>;
  resetPassword: Scalars['Boolean'];
  revokeApiKey: Scalars['Boolean'];
  revokeRefreshTokens: Scalars['Boolean'];
  sendProfileInvitation: Scalars['Boolean'];
  sendQualiphyExamInvite: QualiphyInvitation;
  sendReceipt: SendReceiptPayload;
  signDocument: DocumentSignature;
  startAppointment?: Maybe<Appointment>;
  unassignPaymentAccount: Scalars['Boolean'];
  unassignProcedureProfile: Scalars['Boolean'];
  unassignRole: Scalars['Boolean'];
  unlockForm: Form;
  updateApiKey: ApiKey;
  updateAppointment?: Maybe<Appointment>;
  updateAppointmentRequest?: Maybe<AppointmentRequest>;
  updateAttentiveIntegration?: Maybe<AttentiveIntegration>;
  updateAvailability?: Maybe<Availability>;
  updateAvailabilityCoverage?: Maybe<AvailabilityCoverage>;
  updateCheckout: Checkout;
  updateClientProfile?: Maybe<ClientProfile>;
  updateDiscount?: Maybe<Discount>;
  updateDocument: Document;
  updateEmrInstance: EmrInstance;
  updateFormNote?: Maybe<FormNote>;
  updateFormTemplate?: Maybe<FormTemplate>;
  updateGeoperimeter?: Maybe<Geoperimeter>;
  updateMarketplace?: Maybe<Marketplace>;
  updateMarketplaceGroup?: Maybe<MarketplaceGroup>;
  updateMarketplaceUser?: Maybe<MarketplaceUser>;
  updateMedication: Medication;
  updateMembership: Membership;
  updateMembershipDefinition?: Maybe<MembershipDefinition>;
  updateNavigationGroup?: Maybe<ProcedureBaseDefinitionGroup>;
  updateNotificationsGroup?: Maybe<ProcedureBaseDefinitionGroup>;
  updateOrder: Order;
  updateOrganization?: Maybe<Organization>;
  updatePackage?: Maybe<Package>;
  updatePackageItem: PackageItem;
  updatePackageItemDefinition?: Maybe<PackageItemDefinition>;
  updatePatient: Patient;
  updatePaymentAccount: PaymentAccount;
  updateProcedureBaseDefinition?: Maybe<ProcedureBaseDefinition>;
  updateProcedureBaseDefinitionGroup?: Maybe<ProcedureBaseDefinitionGroup>;
  updateProcedureDefinition?: Maybe<ProcedureDefinition>;
  updateProcedureProfile?: Maybe<ProcedureProfile>;
  updateProfile: Profile;
  updatePromoCode?: Maybe<PromoCode>;
  updateQualiphyExam?: Maybe<QualiphyExam>;
  updateQualiphyIntegration?: Maybe<QualiphyIntegration>;
  updateReviewsGroup?: Maybe<ProcedureBaseDefinitionGroup>;
  updateRole?: Maybe<Role>;
  updateSegmentIntegration?: Maybe<SegmentIntegration>;
  updateSendgridIntegration?: Maybe<SendgridIntegration>;
  updateTwilioIntegration?: Maybe<TwilioIntegration>;
  usePaymentAccount: Scalars['Boolean'];
  verifyEmail: Scalars['Boolean'];
};


export type MutationAcceptAppointmentArgs = {
  input: AcceptAppointmentInput;
};


export type MutationAddOrganizationsToMarketplaceArgs = {
  input: AddOrganizationsToMarketplaceInput;
};


export type MutationApproveAppointmentRequestArgs = {
  input: ApproveAppointmentRequestInput;
};


export type MutationArchiveAppointmentArgs = {
  input: ArchiveAppointmentInput;
};


export type MutationArchiveAppointmentRequestArgs = {
  input: ArchiveAppointmentRequestInput;
};


export type MutationArchiveDiscountArgs = {
  id: Scalars['ID'];
};


export type MutationArchiveDocumentArgs = {
  id: Scalars['ID'];
};


export type MutationArchiveEmrInstanceArgs = {
  id: Scalars['ID'];
};


export type MutationArchiveFormNoteArgs = {
  id: Scalars['ID'];
};


export type MutationArchiveMarketplaceArgs = {
  input: ArchiveMarketplaceInput;
};


export type MutationArchiveMarketplaceGroupArgs = {
  input: ArchiveMarketplaceGroupInput;
};


export type MutationArchiveMembershipDefinitionArgs = {
  id: Scalars['ID'];
};


export type MutationArchiveOrganizationArgs = {
  input: ArchiveOrganizationInput;
};


export type MutationArchivePackageArgs = {
  id: Scalars['ID'];
};


export type MutationArchivePackageItemDefinitionArgs = {
  id: Scalars['ID'];
};


export type MutationArchivePaymentAccountArgs = {
  input: ArchivePaymentAccountInput;
};


export type MutationArchiveProfileArgs = {
  input: ArchiveProfileInput;
};


export type MutationArchivePromoCodeArgs = {
  id: Scalars['ID'];
};


export type MutationAssignEmrInstanceArgs = {
  input: AssignEmrInstanceInput;
};


export type MutationAssignPaymentAccountArgs = {
  input: AssignPaymentAccountInput;
};


export type MutationAssignProcedureProfileArgs = {
  input: AssignProcedureProfileInput;
};


export type MutationAssignRoleArgs = {
  input: AssignRoleInput;
};


export type MutationAuthenticateArgs = {
  input: AuthenticateInput;
};


export type MutationAuthenticateAsArgs = {
  userId: Scalars['ID'];
};


export type MutationCancelAppointmentArgs = {
  input: CancelAppointmentInput;
};


export type MutationCancelAppointmentRequestArgs = {
  input: CancelAppointmentRequestInput;
};


export type MutationCancelMembershipArgs = {
  id: Scalars['ID'];
};


export type MutationChangeEmailArgs = {
  input: ChangeEmailInput;
};


export type MutationCompleteAppointmentArgs = {
  input: CompleteAppointmentInput;
};


export type MutationCompleteCheckoutArgs = {
  input: CompleteCheckoutInput;
};


export type MutationConfirmConsumerEmailArgs = {
  input: ConfirmConsumerEmailInput;
};


export type MutationConfirmConsumerPhoneArgs = {
  input: ConfirmConsumerPhoneInput;
};


export type MutationCreateAllergyIntoleranceArgs = {
  input: CreateAllergyIntoleranceInput;
};


export type MutationCreateApiKeyArgs = {
  input: CreateApiKeyInput;
};


export type MutationCreateApplePayInstrumentArgs = {
  input: PaymentMethodInput;
};


export type MutationCreateApplePaySessionArgs = {
  input: CreateApplePaySessionInput;
};


export type MutationCreateAppointmentArgs = {
  input: CreateAppointmentInput;
};


export type MutationCreateAppointmentRequestArgs = {
  input: CreateAppointmentRequestInput;
};


export type MutationCreateAttachmentArgs = {
  input: CreateAttachmentInput;
};


export type MutationCreateAvailabilityArgs = {
  input: CreateAvailabilityInput;
};


export type MutationCreateAvailabilityCoverageArgs = {
  input: CreateAvailabilityCoverageInput;
};


export type MutationCreateClientProfileArgs = {
  input: CreateClientProfileInput;
};


export type MutationCreateClinicalProcedureArgs = {
  input: CreateClinicalProcedureInput;
};


export type MutationCreateConditionArgs = {
  input: CreateConditionInput;
};


export type MutationCreateDiscountArgs = {
  input: CreateDiscountInput;
};


export type MutationCreateDocumentArgs = {
  input: CreateDocumentInput;
};


export type MutationCreateEmrInstanceArgs = {
  input: CreateEmrInstanceInput;
};


export type MutationCreateFormNoteArgs = {
  input: CreateFormNoteInput;
};


export type MutationCreateFormTemplateArgs = {
  input: CreateFormTemplateInput;
};


export type MutationCreateGeoperimeterArgs = {
  input: CreateGeoperimeterInput;
};


export type MutationCreateImmunizationArgs = {
  input: CreateImmunizationInput;
};


export type MutationCreateLabArgs = {
  input: CreateLabInput;
};


export type MutationCreateMarketplaceArgs = {
  input: CreateMarketplaceInput;
};


export type MutationCreateMarketplaceUserArgs = {
  input: CreateMarketplaceUserInput;
};


export type MutationCreateMedicationArgs = {
  input: CreateMedicationInput;
};


export type MutationCreateMedicationStatementArgs = {
  input: CreateMedicationStatementInput;
};


export type MutationCreateMembershipArgs = {
  input: CreateMembershipInput;
};


export type MutationCreateMembershipDefinitionArgs = {
  input: CreateMembershipDefinitionInput;
};


export type MutationCreateObservationsArgs = {
  input: CreateObservationsInput;
};


export type MutationCreateOrderArgs = {
  input: CreateOrderInput;
};


export type MutationCreateOrganizationArgs = {
  input: CreateOrganizationInput;
};


export type MutationCreatePackageArgs = {
  input: CreatePackageInput;
};


export type MutationCreatePackageItemDefinitionArgs = {
  input: CreatePackageItemDefinitionInput;
};


export type MutationCreatePatientArgs = {
  input: CreatePatientInput;
};


export type MutationCreatePaymentAccountArgs = {
  input: CreatePaymentAccountInput;
};


export type MutationCreatePaymentInstrumentArgs = {
  input: PaymentMethodInput;
};


export type MutationCreateProcedureBaseDefinitionArgs = {
  input: CreateProcedureBaseDefinitionInput;
};


export type MutationCreateProcedureBaseDefinitionGroupArgs = {
  input: CreateProcedureBaseDefinitionGroupInput;
};


export type MutationCreateProcedureBaseDefinitionTagArgs = {
  input: ProcedureBaseDefinitionTagInput;
};


export type MutationCreateProcedureDefinitionArgs = {
  input: CreateProcedureDefinitionInput;
};


export type MutationCreateProcedureProfileArgs = {
  input: CreateProcedureProfileInput;
};


export type MutationCreateProfileArgs = {
  input: CreateProfileInput;
};


export type MutationCreatePromoCodeArgs = {
  input: CreatePromoCodeInput;
};


export type MutationCreateRoleArgs = {
  input: CreateRoleInput;
};


export type MutationCreateUploadUrlArgs = {
  input: CreateUploadUrlInput;
};


export type MutationCreateUserArgs = {
  input: CreateUserInput;
};


export type MutationCreateVitalsArgs = {
  input: CreateVitalsInput;
};


export type MutationDeclineAppointmentArgs = {
  input: DeclineAppointmentInput;
};


export type MutationDeleteAllergyIntoleranceArgs = {
  id: Scalars['ID'];
};


export type MutationDeleteAvailabilityArgs = {
  id: Scalars['ID'];
};


export type MutationDeleteAvailabilityCoverageArgs = {
  id: Scalars['ID'];
};


export type MutationDeleteClinicalProcedureArgs = {
  id: Scalars['ID'];
};


export type MutationDeleteConditionArgs = {
  id: Scalars['ID'];
};


export type MutationDeleteFormTemplateArgs = {
  id: Scalars['ID'];
};


export type MutationDeleteGeoperimeterArgs = {
  id: Scalars['ID'];
};


export type MutationDeleteImmunizationArgs = {
  id: Scalars['ID'];
};


export type MutationDeleteLabArgs = {
  id: Scalars['ID'];
};


export type MutationDeleteMedicationArgs = {
  id: Scalars['ID'];
};


export type MutationDeleteMedicationStatementArgs = {
  id: Scalars['ID'];
};


export type MutationDeleteMembershipArgs = {
  id: Scalars['ID'];
};


export type MutationDeleteObservationArgs = {
  id: Scalars['ID'];
};


export type MutationDeleteOrderArgs = {
  id: Scalars['ID'];
};


export type MutationDeletePackageItemArgs = {
  id: Scalars['ID'];
};


export type MutationDeletePaymentInstrumentArgs = {
  id: Scalars['ID'];
};


export type MutationDeleteProcedureBaseDefinitionArgs = {
  id: Scalars['ID'];
};


export type MutationDeleteProcedureBaseDefinitionGroupArgs = {
  id: Scalars['ID'];
};


export type MutationDeleteProcedureBaseDefinitionTagArgs = {
  input: ProcedureBaseDefinitionTagInput;
};


export type MutationDeleteProcedureDefinitionArgs = {
  id: Scalars['ID'];
};


export type MutationDeleteProcedureProfileArgs = {
  id: Scalars['ID'];
};


export type MutationDeleteQualiphyExamArgs = {
  id: Scalars['ID'];
};


export type MutationDeleteReportArgs = {
  id: Scalars['ID'];
};


export type MutationDeleteRoleArgs = {
  id: Scalars['ID'];
};


export type MutationDeleteVitalArgs = {
  id: Scalars['ID'];
};


export type MutationFcmTokenArgs = {
  input: FcmTokenInput;
};


export type MutationGrantPackageArgs = {
  input: GrantPackageInput;
};


export type MutationJoinOrganizationArgs = {
  input: JoinOrganizationInput;
};


export type MutationLeaveOrganizationArgs = {
  input: LeaveOrganizationInput;
};


export type MutationLockFormArgs = {
  input: LockFormInput;
};


export type MutationLogoutArgs = {
  fcmToken?: InputMaybe<Scalars['String']>;
};


export type MutationReactivateMembershipArgs = {
  id: Scalars['ID'];
};


export type MutationRefreshTokensArgs = {
  refreshToken: Scalars['String'];
};


export type MutationRefundCheckoutArgs = {
  input: RefundCheckoutInput;
};


export type MutationRegisterArgs = {
  input: RegisterInput;
};


export type MutationRemoveOrganizationsFromMarketplaceArgs = {
  input: RemoveOrganizationsFromMarketplaceInput;
};


export type MutationReportAppointmentsArgs = {
  input: ReportAppointmentsInput;
};


export type MutationReportPersonnelArgs = {
  input: ReportPersonnelInput;
};


export type MutationRequestChangeEmailArgs = {
  newEmail: Scalars['String'];
};


export type MutationRequestConfirmConsumerEmailArgs = {
  input: RequestConfirmConsumerEmailInput;
};


export type MutationRequestConfirmConsumerPhoneArgs = {
  input: RequestConfirmConsumerPhoneInput;
};


export type MutationRequestResetPasswordArgs = {
  email: Scalars['String'];
};


export type MutationResendInvitationCodeArgs = {
  email: Scalars['String'];
};


export type MutationResendQualiphyExamInviteArgs = {
  id: Scalars['String'];
};


export type MutationResetAppointmentArgs = {
  input: ResetAppointmentInput;
};


export type MutationResetPasswordArgs = {
  input: ResetPasswordInput;
};


export type MutationRevokeApiKeyArgs = {
  kid: Scalars['String'];
};


export type MutationRevokeRefreshTokensArgs = {
  userId?: InputMaybe<Scalars['ID']>;
};


export type MutationSendProfileInvitationArgs = {
  input: SendInvitationInput;
};


export type MutationSendQualiphyExamInviteArgs = {
  id: Scalars['String'];
};


export type MutationSendReceiptArgs = {
  input: SendReceiptInput;
};


export type MutationSignDocumentArgs = {
  input: SignDocumentInput;
};


export type MutationStartAppointmentArgs = {
  input: StartAppointmentInput;
};


export type MutationUnassignPaymentAccountArgs = {
  input: UnassignPaymentAccountInput;
};


export type MutationUnassignProcedureProfileArgs = {
  input: UnassignProcedureProfileInput;
};


export type MutationUnassignRoleArgs = {
  input: UnassignRoleInput;
};


export type MutationUnlockFormArgs = {
  input: UnlockFormInput;
};


export type MutationUpdateApiKeyArgs = {
  input: UpdateApiKeyInput;
};


export type MutationUpdateAppointmentArgs = {
  input: UpdateAppointmentInput;
};


export type MutationUpdateAppointmentRequestArgs = {
  input: UpdateAppointmentRequestInput;
};


export type MutationUpdateAttentiveIntegrationArgs = {
  input: UpdateAttentiveIntegrationInput;
};


export type MutationUpdateAvailabilityArgs = {
  input: UpdateAvailabilityInput;
};


export type MutationUpdateAvailabilityCoverageArgs = {
  input: UpdateAvailabilityCoverageInput;
};


export type MutationUpdateCheckoutArgs = {
  input: UpdateCheckoutInput;
};


export type MutationUpdateClientProfileArgs = {
  input: UpdateClientProfileInput;
};


export type MutationUpdateDiscountArgs = {
  input: UpdateDiscountInput;
};


export type MutationUpdateDocumentArgs = {
  input: UpdateDocumentInput;
};


export type MutationUpdateEmrInstanceArgs = {
  input: UpdateEmrInstanceInput;
};


export type MutationUpdateFormNoteArgs = {
  input: UpdateFormNoteInput;
};


export type MutationUpdateFormTemplateArgs = {
  input: UpdateFormTemplateInput;
};


export type MutationUpdateGeoperimeterArgs = {
  input: UpdateGeoperimeterInput;
};


export type MutationUpdateMarketplaceArgs = {
  input: UpdateMarketplaceInput;
};


export type MutationUpdateMarketplaceGroupArgs = {
  input: UpdateMarketplaceGroupInput;
};


export type MutationUpdateMarketplaceUserArgs = {
  input: UpdateMarketplaceUserInput;
};


export type MutationUpdateMedicationArgs = {
  input: UpdateMedicationInput;
};


export type MutationUpdateMembershipArgs = {
  input: UpdateMembershipInput;
};


export type MutationUpdateMembershipDefinitionArgs = {
  input: UpdateMembershipDefinitionInput;
};


export type MutationUpdateNavigationGroupArgs = {
  input: UpdateConfigGroupInput;
};


export type MutationUpdateNotificationsGroupArgs = {
  input: UpdateConfigGroupInput;
};


export type MutationUpdateOrderArgs = {
  input: UpdateOrderInput;
};


export type MutationUpdateOrganizationArgs = {
  input: UpdateOrganizationInput;
};


export type MutationUpdatePackageArgs = {
  input: UpdatePackageInput;
};


export type MutationUpdatePackageItemArgs = {
  input: UpdatePackageItemInput;
};


export type MutationUpdatePackageItemDefinitionArgs = {
  input: UpdatePackageItemDefinitionInput;
};


export type MutationUpdatePatientArgs = {
  input: UpdatePatientInput;
};


export type MutationUpdatePaymentAccountArgs = {
  input: UpdatePaymentAccountInput;
};


export type MutationUpdateProcedureBaseDefinitionArgs = {
  input: UpdateProcedureBaseDefinitionInput;
};


export type MutationUpdateProcedureBaseDefinitionGroupArgs = {
  input: UpdateProcedureBaseDefinitionGroupInput;
};


export type MutationUpdateProcedureDefinitionArgs = {
  input: UpdateProcedureDefinitionInput;
};


export type MutationUpdateProcedureProfileArgs = {
  input: UpdateProcedureProfileInput;
};


export type MutationUpdateProfileArgs = {
  input: UpdateProfileInput;
};


export type MutationUpdatePromoCodeArgs = {
  input: UpdatePromoCodeInput;
};


export type MutationUpdateQualiphyExamArgs = {
  input: UpdateQualiphyExamInput;
};


export type MutationUpdateQualiphyIntegrationArgs = {
  input: UpdateQualiphyIntegrationInput;
};


export type MutationUpdateReviewsGroupArgs = {
  input: UpdateConfigGroupInput;
};


export type MutationUpdateRoleArgs = {
  input: UpdateRoleInput;
};


export type MutationUpdateSegmentIntegrationArgs = {
  input: UpdateSegmentIntegrationInput;
};


export type MutationUpdateSendgridIntegrationArgs = {
  input: UpdateSendgridIntegrationInput;
};


export type MutationUpdateTwilioIntegrationArgs = {
  input: UpdateTwilioIntegrationInput;
};


export type MutationUsePaymentAccountArgs = {
  input: UsePaymentAccountInput;
};


export type MutationVerifyEmailArgs = {
  input: VerifyEmailInput;
};

export type Observation = {
  __typename?: 'Observation';
  code: Scalars['String'];
  createdAt: Scalars['DateTime'];
  createdById: Scalars['ID'];
  id: Scalars['ID'];
  note?: Maybe<Scalars['String']>;
  updatedAt: Scalars['DateTime'];
  value: Scalars['String'];
};

export type ObservationInput = {
  code: Scalars['String'];
  note?: InputMaybe<Scalars['String']>;
  value: Scalars['String'];
};

export type Order = {
  __typename?: 'Order';
  archivedAt?: Maybe<Scalars['DateTime']>;
  createdAt: Scalars['DateTime'];
  expiresAt: Scalars['DateTime'];
  fillCount: Scalars['Int'];
  id: Scalars['ID'];
  note?: Maybe<Scalars['String']>;
  organizations: Array<Organization>;
  patient: Patient;
  procedureDefs: Array<ProcedureDefinition>;
  profile?: Maybe<Profile>;
  providerType: ProviderType;
  refills: Scalars['Int'];
  startsAt: Scalars['DateTime'];
  updatedAt: Scalars['DateTime'];
};

export type Organization = {
  __typename?: 'Organization';
  address?: Maybe<Scalars['String']>;
  appointments: AppointmentPage;
  archivedAt?: Maybe<Scalars['DateTime']>;
  availabilityCoverageRanges: Array<AvailabilityCoverageRange>;
  availabilityCoverages: Array<AvailabilityCoverage>;
  clientProfiles: ClientProfilePage;
  createdAt: Scalars['DateTime'];
  email?: Maybe<Scalars['String']>;
  emrInstance?: Maybe<EmrInstance>;
  emrInstanceId?: Maybe<Scalars['ID']>;
  enablePractitionerSms: Scalars['Boolean'];
  enableReceiptSending: Scalars['Boolean'];
  geoperimeters: Array<Geoperimeter>;
  googleReviewsUrl?: Maybe<Scalars['String']>;
  id: Scalars['ID'];
  marketplaces: Array<Marketplace>;
  medications: Array<Medication>;
  name: Scalars['String'];
  paymentAccountId?: Maybe<Scalars['ID']>;
  paymentAccounts: Array<PaymentAccount>;
  phone?: Maybe<Scalars['String']>;
  procedureDefs: Array<ProcedureDefinition>;
  procedureProfiles: Array<ProcedureProfile>;
  profiles: Array<Profile>;
  providesAtClinic: Scalars['Boolean'];
  qualiphy?: Maybe<QualiphyIntegration>;
  qualiphyEnabled: Scalars['Boolean'];
  roles: Array<Role>;
  slackWebhookUrl?: Maybe<Scalars['String']>;
  state?: Maybe<StateCode>;
  tzid: Scalars['String'];
  updatedAt: Scalars['DateTime'];
};


export type OrganizationAppointmentsArgs = {
  page?: InputMaybe<AppointmentPageInput>;
};


export type OrganizationAvailabilityCoverageRangesArgs = {
  input: AvailabilityCoverageRangeInput;
};


export type OrganizationClientProfilesArgs = {
  page?: InputMaybe<ClientProfilePageInput>;
};

export type Package = {
  __typename?: 'Package';
  advertise: Scalars['Boolean'];
  archivedAt?: Maybe<Scalars['DateTime']>;
  createdAt: Scalars['DateTime'];
  description?: Maybe<Scalars['String']>;
  id: Scalars['ID'];
  list?: Maybe<Scalars['String']>;
  marketplaceId: Scalars['ID'];
  name: Scalars['String'];
  packageItemDefinitions: Array<PackageItemDefinition>;
  price: Scalars['Int'];
  updatedAt: Scalars['DateTime'];
};

export type PackageItem = {
  __typename?: 'PackageItem';
  balance: Scalars['Int'];
  costBasis: Scalars['Int'];
  createdAt: Scalars['DateTime'];
  expiresAt?: Maybe<Scalars['DateTime']>;
  groupNames: Scalars['String'];
  id: Scalars['ID'];
  marketplaceName: Scalars['String'];
  marketplaceUserId: Scalars['ID'];
  membershipId?: Maybe<Scalars['ID']>;
  packageItemDefinitionId: Scalars['ID'];
  packageName: Scalars['String'];
  points: Scalars['Int'];
  primaryOrganizationId?: Maybe<Scalars['ID']>;
  totalPoints: Scalars['Int'];
  updatedAt: Scalars['DateTime'];
};

export type PackageItemDefinition = {
  __typename?: 'PackageItemDefinition';
  archivedAt?: Maybe<Scalars['DateTime']>;
  createdAt: Scalars['DateTime'];
  id: Scalars['ID'];
  packageId: Scalars['ID'];
  points: Scalars['Int'];
  procedureGroups: Array<Scalars['String']>;
  updatedAt: Scalars['DateTime'];
};

export type Participant = ClientProfile | Profile;

export enum ParticipantType {
  Patient = 'PATIENT',
  Practitioner = 'PRACTITIONER'
}

export enum ParticipationStatus {
  Accepted = 'ACCEPTED',
  Declined = 'DECLINED',
  NeedsAction = 'NEEDS_ACTION',
  Tentative = 'TENTATIVE'
}

export type Patient = {
  __typename?: 'Patient';
  allergies?: Maybe<Scalars['String']>;
  allergyRecords: Array<AllergyIntolerance>;
  archivedAt?: Maybe<Scalars['DateTime']>;
  attachments?: Maybe<Array<Document>>;
  clientProfileId: Scalars['ID'];
  clinicalProcedureRecords: Array<ClinicalProcedure>;
  conditionRecords: Array<Condition>;
  conditions?: Maybe<Scalars['String']>;
  createdAt: Scalars['DateTime'];
  emrInstanceId: Scalars['ID'];
  id: Scalars['ID'];
  immunizationRecords: Array<Immunization>;
  immunizations?: Maybe<Scalars['String']>;
  labRecords: Array<Lab>;
  labs?: Maybe<Scalars['String']>;
  medicationRecords: Array<MedicationStatement>;
  medications?: Maybe<Scalars['String']>;
  orders: Array<Order>;
  procedures?: Maybe<Scalars['String']>;
  signedConsentForms: Array<DocumentSignature>;
  updatedAt: Scalars['DateTime'];
  vitals: Array<VitalSign>;
};

export type Payment = {
  __typename?: 'Payment';
  amount: Scalars['Int'];
  amountRequested: Scalars['Int'];
  amountReversed?: Maybe<Scalars['Int']>;
  archivedAt?: Maybe<Scalars['DateTime']>;
  createdAt: Scalars['DateTime'];
  description: Scalars['String'];
  fee: Scalars['Int'];
  id: Scalars['ID'];
  isDeposit: Scalars['Boolean'];
  status: PaymentStatus;
  type: PaymentType;
  updatedAt: Scalars['DateTime'];
};

export type PaymentAccount = {
  __typename?: 'PaymentAccount';
  archivedAt?: Maybe<Scalars['DateTime']>;
  createdAt: Scalars['DateTime'];
  enabled: Scalars['Boolean'];
  id: Scalars['ID'];
  label: Scalars['String'];
  marketplaces: Array<Marketplace>;
  onboardingUrl?: Maybe<Scalars['String']>;
  organizations: Array<Organization>;
  platform: PaymentAccountPlatform;
  state: PaymentAccountState;
  updatedAt: Scalars['DateTime'];
};

export enum PaymentAccountPlatform {
  Finix = 'FINIX'
}

export enum PaymentAccountState {
  Approved = 'APPROVED',
  Created = 'CREATED',
  Onboarding = 'ONBOARDING',
  Provisioning = 'PROVISIONING',
  Rejected = 'REJECTED'
}

export enum PaymentCollectionMethod {
  CollectDeposit = 'collect_deposit',
  CollectOnConfirmation = 'collect_on_confirmation',
  CollectOnSite = 'collect_on_site'
}

export enum PaymentDepositType {
  FixedAmount = 'fixed_amount',
  Percentage = 'percentage'
}

export type PaymentIdentityInput = {
  email?: InputMaybe<Scalars['String']>;
  firstName?: InputMaybe<Scalars['String']>;
  lastName?: InputMaybe<Scalars['String']>;
  phone?: InputMaybe<Scalars['String']>;
  postalCode?: InputMaybe<Scalars['String']>;
};

export type PaymentInstrument = {
  __typename?: 'PaymentInstrument';
  archivedAt?: Maybe<Scalars['DateTime']>;
  brand?: Maybe<Scalars['String']>;
  createdAt: Scalars['DateTime'];
  expirationMonth?: Maybe<Scalars['Float']>;
  expirationYear?: Maybe<Scalars['Float']>;
  id: Scalars['ID'];
  lastFour?: Maybe<Scalars['String']>;
  processor: PaymentAccountPlatform;
  type: PaymentInstrumentType;
  updatedAt: Scalars['DateTime'];
};

export enum PaymentInstrumentType {
  Card = 'CARD'
}

export type PaymentMethodCardInput = {
  identity?: InputMaybe<PaymentIdentityInput>;
  thirdPartyToken?: InputMaybe<Scalars['String']>;
  token?: InputMaybe<Scalars['String']>;
};

export type PaymentMethodInput = {
  card?: InputMaybe<PaymentMethodCardInput>;
  marketplaceUserId?: InputMaybe<Scalars['ID']>;
  paymentInstrumentId?: InputMaybe<Scalars['ID']>;
  save?: InputMaybe<Scalars['Boolean']>;
};

export enum PaymentStatus {
  Accepted = 'ACCEPTED',
  Authorized = 'AUTHORIZED',
  Failed = 'FAILED',
  Pending = 'PENDING'
}

export enum PaymentType {
  Credit = 'CREDIT'
}

export type Payout = {
  __typename?: 'Payout';
  amount: Scalars['Int'];
  amountRequested: Scalars['Int'];
  amountReversed?: Maybe<Scalars['Int']>;
  archivedAt?: Maybe<Scalars['DateTime']>;
  checkout?: Maybe<Checkout>;
  checkoutId: Scalars['ID'];
  createdAt: Scalars['DateTime'];
  description: Scalars['String'];
  id: Scalars['ID'];
  status: PayoutStatus;
  updatedAt: Scalars['DateTime'];
};

export type PayoutFilterInput = {
  amount?: InputMaybe<FilterIntegerInput>;
  amountRequested?: InputMaybe<FilterIntegerInput>;
  amountReversed?: InputMaybe<FilterIntegerInput>;
  description?: InputMaybe<FilterStringInput>;
  id?: InputMaybe<FilterIntegerInput>;
  marketplaceId?: InputMaybe<FilterIntegerInput>;
  status?: InputMaybe<FilterStringInput>;
};

export type PayoutPage = {
  __typename?: 'PayoutPage';
  data: Array<Payout>;
  totalCount: Scalars['Int'];
};

export type PayoutPageInput = {
  filter?: InputMaybe<PayoutFilterInput>;
  limit?: InputMaybe<Scalars['Int']>;
  offset?: InputMaybe<Scalars['Int']>;
  sort?: InputMaybe<Array<PayoutSortInput>>;
};

export enum PayoutSortField {
  Amount = 'AMOUNT',
  Amountrequested = 'AMOUNTREQUESTED',
  Amountreversed = 'AMOUNTREVERSED',
  Createdat = 'CREATEDAT',
  Description = 'DESCRIPTION',
  Id = 'ID',
  Marketplaceid = 'MARKETPLACEID',
  Status = 'STATUS',
  Updatedat = 'UPDATEDAT'
}

export type PayoutSortInput = {
  direction: SortDirection;
  field: PayoutSortField;
};

export enum PayoutStatus {
  Accepted = 'ACCEPTED',
  Failed = 'FAILED',
  Pending = 'PENDING'
}

export type PreviewCheckoutItemsInput = {
  applyPackages?: InputMaybe<Scalars['Boolean']>;
  latitude?: InputMaybe<Scalars['Float']>;
  longitude?: InputMaybe<Scalars['Float']>;
  marketplaceUserId?: InputMaybe<Scalars['ID']>;
  membershipDefinitionId?: InputMaybe<Scalars['ID']>;
  /** Requests a checkout preview for each organizationId */
  organizationIds: Array<Scalars['ID']>;
  procedureBaseDefIds: Array<Scalars['ID']>;
  promoCode?: InputMaybe<Scalars['String']>;
};

export type ProcedureBaseDefLayoutInput = {
  itemIndex: Scalars['Float'];
  layout: Scalars['String'];
  type: ProcedureBaseDefinitionLayoutType;
};

export type ProcedureBaseDefinition = {
  __typename?: 'ProcedureBaseDefinition';
  addOns: Array<Scalars['String']>;
  archivedAt?: Maybe<Scalars['DateTime']>;
  category?: Maybe<Scalars['String']>;
  createdAt: Scalars['DateTime'];
  description: Scalars['String'];
  duration: Scalars['Float'];
  id: Scalars['ID'];
  ingredients?: Maybe<Scalars['String']>;
  layouts: Array<ProcedureBaseDefinitionLayout>;
  marketplaceId: Scalars['ID'];
  name: Scalars['String'];
  points?: Maybe<Scalars['Int']>;
  tagline?: Maybe<Scalars['String']>;
  tags: Array<Scalars['String']>;
  thumbnail?: Maybe<Scalars['String']>;
  updatedAt: Scalars['DateTime'];
};

export type ProcedureBaseDefinitionGroup = {
  __typename?: 'ProcedureBaseDefinitionGroup';
  banner?: Maybe<Scalars['String']>;
  bgcolor?: Maybe<Scalars['String']>;
  description?: Maybe<Scalars['String']>;
  featuredBaseDefs: Array<ProcedureBaseDefinition>;
  fontColor?: Maybe<Scalars['String']>;
  id: Scalars['ID'];
  marketplaceId: Scalars['ID'];
  name: Scalars['String'];
  procedureBaseDefGroups: Array<ProcedureBaseDefinitionGroup>;
  procedureBaseDefs: Array<ProcedureBaseDefinition>;
  thumbnail?: Maybe<Scalars['String']>;
  type: ProcedureGroupType;
};

export type ProcedureBaseDefinitionLayout = {
  __typename?: 'ProcedureBaseDefinitionLayout';
  id: Scalars['ID'];
  itemIndex: Scalars['Float'];
  layout: Scalars['String'];
  procedureBaseDefinitionId: Scalars['ID'];
  type: ProcedureBaseDefinitionLayoutType;
};

export enum ProcedureBaseDefinitionLayoutType {
  Block = 'BLOCK',
  Faq = 'FAQ',
  Grid = 'GRID',
  List = 'LIST'
}

export type ProcedureBaseDefinitionTagInput = {
  procedureBaseDefinitionId: Scalars['ID'];
  tag: Scalars['String'];
};

export type ProcedureDefinition = {
  __typename?: 'ProcedureDefinition';
  archivedAt?: Maybe<Scalars['DateTime']>;
  assessmentFormTemplateId?: Maybe<Scalars['ID']>;
  baseDefinitions: Array<ProcedureBaseDefinition>;
  consentForms: Array<Document>;
  createdAt: Scalars['DateTime'];
  description: Scalars['String'];
  duration: Scalars['Float'];
  id: Scalars['ID'];
  interventionFormTemplateId?: Maybe<Scalars['ID']>;
  medicationProtocols: Array<MedicationProtocol>;
  name: Scalars['String'];
  organizationId: Scalars['ID'];
  price: Scalars['Float'];
  updatedAt: Scalars['DateTime'];
};

export enum ProcedureGroupType {
  Consumer = 'CONSUMER',
  Navigation = 'NAVIGATION',
  Notifications = 'NOTIFICATIONS',
  Reviews = 'REVIEWS',
  UserDefined = 'USER_DEFINED'
}

export type ProcedureProfile = {
  __typename?: 'ProcedureProfile';
  createdAt: Scalars['DateTime'];
  id: Scalars['ID'];
  name: Scalars['String'];
  procedureDefs: Array<ProcedureDefinition>;
  profiles: Array<Profile>;
  updatedAt: Scalars['DateTime'];
};

export type Profile = {
  __typename?: 'Profile';
  address: Scalars['String'];
  allowSmsNotifications: Scalars['Boolean'];
  appointments: AppointmentPage;
  archivedAt?: Maybe<Scalars['DateTime']>;
  availabilities: Array<Availability>;
  availableUntil?: Maybe<Scalars['DateTime']>;
  color: Scalars['String'];
  createdAt: Scalars['DateTime'];
  email: Scalars['String'];
  familyName: Scalars['String'];
  givenName: Scalars['String'];
  id: Scalars['ID'];
  organization?: Maybe<Organization>;
  phone: Scalars['String'];
  pid: Scalars['String'];
  procedureProfiles: Array<ProcedureProfile>;
  roles: Array<Role>;
  title: Scalars['String'];
  tzid: Scalars['String'];
  updatedAt: Scalars['DateTime'];
  userId?: Maybe<Scalars['ID']>;
};


export type ProfileAppointmentsArgs = {
  page?: InputMaybe<AppointmentPageInput>;
};

export type PromoCode = {
  __typename?: 'PromoCode';
  activationEndDate: Scalars['DateTime'];
  activationStartDate: Scalars['DateTime'];
  active: Scalars['Boolean'];
  archivedAt?: Maybe<Scalars['DateTime']>;
  code: Scalars['String'];
  createdAt: Scalars['DateTime'];
  discountType: PromoCodeDiscountType;
  discountValue: Scalars['Int'];
  id: Scalars['ID'];
  marketplaceId: Scalars['ID'];
  minimumBookingAmount?: Maybe<Scalars['Int']>;
  name: Scalars['String'];
  organizations: Array<Organization>;
  procedureGroups: Array<ProcedureBaseDefinitionGroup>;
  updatedAt: Scalars['DateTime'];
  usage: Array<PromoCodeUsage>;
  usageEndDate: Scalars['DateTime'];
  usageLimitPerUser?: Maybe<Scalars['Int']>;
  usageLimitTotal?: Maybe<Scalars['Int']>;
  usageStartDate: Scalars['DateTime'];
};

export enum PromoCodeDiscountType {
  FixedAmount = 'FIXED_AMOUNT',
  Percentage = 'PERCENTAGE'
}

export type PromoCodeUsage = {
  __typename?: 'PromoCodeUsage';
  checkoutId?: Maybe<Scalars['ID']>;
  createdAt: Scalars['DateTime'];
  discountAmount: Scalars['Int'];
  id: Scalars['ID'];
  marketplaceUserId: Scalars['ID'];
  promoCodeId: Scalars['ID'];
  updatedAt: Scalars['DateTime'];
  usedAt: Scalars['DateTime'];
};

export type PromoCodeValidationResult = {
  __typename?: 'PromoCodeValidationResult';
  discountAmount?: Maybe<Scalars['Int']>;
  error?: Maybe<Scalars['String']>;
  valid: Scalars['Boolean'];
};

export enum ProviderType {
  Profile = 'PROFILE',
  Qualiphy = 'QUALIPHY'
}

export type QualiphyExam = {
  __typename?: 'QualiphyExam';
  archived: Scalars['Boolean'];
  expiresAfter: Scalars['Int'];
  id: Scalars['ID'];
  index: Scalars['Int'];
  procedureDefinitions?: Maybe<Array<Scalars['String']>>;
  qualiphyId: Scalars['ID'];
  qualiphyIntegrationId: Scalars['ID'];
  refills: Scalars['Int'];
  status?: Maybe<QualiphyInvitationStatus>;
  title: Scalars['String'];
};

export type QualiphyIntegration = {
  __typename?: 'QualiphyIntegration';
  apiKey?: Maybe<Scalars['String']>;
  apiKeyDescription?: Maybe<Scalars['String']>;
  enabled: Scalars['Boolean'];
  exams?: Maybe<Array<QualiphyExam>>;
  id: Scalars['ID'];
  organizationId: Scalars['ID'];
};

export type QualiphyInvitation = {
  __typename?: 'QualiphyInvitation';
  exams?: Maybe<Array<QualiphyExam>>;
  expiration: Scalars['DateTime'];
  id: Scalars['ID'];
  meetingUrl?: Maybe<Scalars['String']>;
  meetingUuid?: Maybe<Scalars['String']>;
  organizationId: Scalars['ID'];
};

export enum QualiphyInvitationStatus {
  Approved = 'APPROVED',
  Deferred = 'DEFERRED',
  Pending = 'PENDING',
  Rejected = 'REJECTED'
}

export type Query = {
  __typename?: 'Query';
  appointment?: Maybe<Appointment>;
  appointmentRequest?: Maybe<AppointmentRequest>;
  appointmentRequests?: Maybe<AppointmentRequestPage>;
  checkout: Checkout;
  checkouts: CheckoutPage;
  clientProfile?: Maybe<ClientProfile>;
  clientProfiles: ClientProfilePage;
  emrInstance: EmrInstance;
  emrInstances: Array<EmrInstance>;
  marketplace?: Maybe<Marketplace>;
  marketplaceGroup?: Maybe<MarketplaceGroup>;
  marketplaceGroups: Array<MarketplaceGroup>;
  marketplaceUser: MarketplaceUser;
  marketplaceUsers: MarketplaceUserPage;
  marketplaces: Array<Marketplace>;
  membershipDefinition?: Maybe<MembershipDefinition>;
  metrics: Array<MetricData>;
  organization?: Maybe<Organization>;
  organizations: Array<Organization>;
  package?: Maybe<Package>;
  paymentAccount: PaymentAccount;
  paymentAccounts: Array<PaymentAccount>;
  payout: Payout;
  payouts: PayoutPage;
  previewCheckoutItems: Array<Array<CheckoutItem>>;
  procedureBaseDefinition?: Maybe<ProcedureBaseDefinition>;
  procedureDefinition?: Maybe<ProcedureDefinition>;
  profile?: Maybe<Profile>;
  profiles: Array<Profile>;
  promoCode?: Maybe<PromoCode>;
  promoCodes: Array<PromoCode>;
  qualiphyExam?: Maybe<QualiphyExam>;
  report?: Maybe<Report>;
  reports: ReportPage;
  roles: Array<Role>;
  user?: Maybe<User>;
  users: Array<User>;
  validatePromoCode: PromoCodeValidationResult;
  viewer?: Maybe<User>;
};


export type QueryAppointmentArgs = {
  id: Scalars['ID'];
};


export type QueryAppointmentRequestArgs = {
  id: Scalars['ID'];
};


export type QueryAppointmentRequestsArgs = {
  page?: InputMaybe<AppointmentRequestPageInput>;
};


export type QueryCheckoutArgs = {
  id: Scalars['ID'];
};


export type QueryCheckoutsArgs = {
  page?: InputMaybe<CheckoutPageInput>;
};


export type QueryClientProfileArgs = {
  id: Scalars['ID'];
};


export type QueryClientProfilesArgs = {
  page?: InputMaybe<ClientProfilePageInput>;
};


export type QueryEmrInstanceArgs = {
  id: Scalars['ID'];
};


export type QueryMarketplaceArgs = {
  id: Scalars['ID'];
};


export type QueryMarketplaceGroupArgs = {
  id: Scalars['ID'];
};


export type QueryMarketplaceUserArgs = {
  id: Scalars['ID'];
};


export type QueryMarketplaceUsersArgs = {
  page?: InputMaybe<MarketplaceUserPageInput>;
};


export type QueryMembershipDefinitionArgs = {
  id: Scalars['ID'];
};


export type QueryMetricsArgs = {
  input: MetricsInput;
};


export type QueryOrganizationArgs = {
  id: Scalars['ID'];
};


export type QueryPackageArgs = {
  id: Scalars['ID'];
};


export type QueryPaymentAccountArgs = {
  id: Scalars['ID'];
};


export type QueryPayoutArgs = {
  id: Scalars['ID'];
};


export type QueryPayoutsArgs = {
  page?: InputMaybe<PayoutPageInput>;
};


export type QueryPreviewCheckoutItemsArgs = {
  input: PreviewCheckoutItemsInput;
};


export type QueryProcedureBaseDefinitionArgs = {
  id: Scalars['ID'];
};


export type QueryProcedureDefinitionArgs = {
  id: Scalars['ID'];
};


export type QueryProfileArgs = {
  id: Scalars['ID'];
};


export type QueryPromoCodeArgs = {
  id: Scalars['ID'];
};


export type QueryPromoCodesArgs = {
  marketplaceId: Scalars['ID'];
};


export type QueryQualiphyExamArgs = {
  id: Scalars['ID'];
};


export type QueryReportArgs = {
  id: Scalars['ID'];
};


export type QueryReportsArgs = {
  page?: InputMaybe<ReportPageInput>;
};


export type QueryUserArgs = {
  id: Scalars['ID'];
};


export type QueryValidatePromoCodeArgs = {
  input: ValidatePromoCodeInput;
};

export type RefundCheckoutInput = {
  /** Required to prevent race conditions */
  balanceAfterRefund: Scalars['Int'];
  checkoutId?: InputMaybe<Scalars['ID']>;
  refundAmount: Scalars['Int'];
};

export type RegisterInput = {
  code: Scalars['String'];
  email: Scalars['String'];
  password: Scalars['String'];
};

export type RemoveOrganizationsFromMarketplaceInput = {
  marketplaceId: Scalars['ID'];
  organizationIds: Array<Scalars['ID']>;
};

export enum RepeatFrequencyType {
  Weekly = 'WEEKLY'
}

export type RepeatRule = {
  __typename?: 'RepeatRule';
  availabilityId?: Maybe<Scalars['ID']>;
  byWeekday: Array<RepeatWeekdayType>;
  frequency: RepeatFrequencyType;
  id: Scalars['ID'];
  until?: Maybe<Scalars['DateTime']>;
};

export type RepeatRuleInput = {
  byWeekday: Array<RepeatWeekdayType>;
  frequency: RepeatFrequencyType;
  until?: InputMaybe<Scalars['DateTime']>;
};

export enum RepeatWeekdayType {
  Fr = 'FR',
  Mo = 'MO',
  Sa = 'SA',
  Su = 'SU',
  Th = 'TH',
  Tu = 'TU',
  We = 'WE'
}

export type Report = {
  __typename?: 'Report';
  createdAt: Scalars['DateTime'];
  downloadUrl: Scalars['String'];
  filename?: Maybe<Scalars['String']>;
  id: Scalars['ID'];
  organizationId: Scalars['ID'];
  status: ReportStatus;
  type: ReportType;
  updatedAt: Scalars['DateTime'];
  user: User;
  userId: Scalars['ID'];
  userName?: Maybe<Scalars['String']>;
};

export type ReportAppointmentsInput = {
  dateRange: Array<Scalars['DateTime']>;
  organizationId: Scalars['ID'];
};

export type ReportFilterInput = {
  createdAt?: InputMaybe<FilterDateInput>;
  filename?: InputMaybe<FilterStringInput>;
  organizationId?: InputMaybe<FilterIntegerInput>;
  type?: InputMaybe<Array<ReportType>>;
  updatedAt?: InputMaybe<FilterDateInput>;
};

export type ReportPage = {
  __typename?: 'ReportPage';
  data: Array<Report>;
  totalCount: Scalars['Int'];
};

export type ReportPageInput = {
  filter?: InputMaybe<ReportFilterInput>;
  limit?: InputMaybe<Scalars['Int']>;
  offset?: InputMaybe<Scalars['Int']>;
  sort?: InputMaybe<Array<ReportSortInput>>;
};

export type ReportPersonnelInput = {
  dateRange: Array<Scalars['DateTime']>;
  organizationId: Scalars['ID'];
  profileIds?: InputMaybe<Array<Scalars['ID']>>;
};

export enum ReportSortField {
  Createdat = 'CREATEDAT',
  Filename = 'FILENAME',
  Id = 'ID',
  Status = 'STATUS',
  Type = 'TYPE',
  Updatedat = 'UPDATEDAT'
}

export type ReportSortInput = {
  direction: SortDirection;
  field: ReportSortField;
};

export enum ReportStatus {
  Completed = 'COMPLETED',
  Failed = 'FAILED',
  Processing = 'PROCESSING'
}

export enum ReportType {
  Appointments = 'APPOINTMENTS',
  Personnel = 'PERSONNEL'
}

export type RequestConfirmConsumerEmailInput = {
  email: Scalars['String'];
  marketplaceId: Scalars['ID'];
  marketplaceUserId: Scalars['ID'];
};

export type RequestConfirmConsumerPhoneInput = {
  marketplaceId: Scalars['ID'];
  marketplaceUserId: Scalars['ID'];
};

export type ResetAppointmentInput = {
  appointmentId: Scalars['ID'];
};

export type ResetPasswordInput = {
  password: Scalars['String'];
  token: Scalars['String'];
};

export type Role = {
  __typename?: 'Role';
  createdAt: Scalars['DateTime'];
  id: Scalars['ID'];
  name: Scalars['String'];
  permissions: Array<Scalars['String']>;
  resourceId?: Maybe<Scalars['ID']>;
  scope: RoleScope;
  type: RoleType;
  updatedAt: Scalars['DateTime'];
};

export enum RoleScope {
  Marketplace = 'MARKETPLACE',
  Organization = 'ORGANIZATION',
  Root = 'ROOT'
}

export enum RoleType {
  Custom = 'CUSTOM',
  System = 'SYSTEM',
  SystemBase = 'SYSTEM_BASE'
}

export type SegmentIntegration = {
  __typename?: 'SegmentIntegration';
  enabled: Scalars['Boolean'];
  id: Scalars['ID'];
  writeKeyDescription?: Maybe<Scalars['String']>;
};

export type SendInvitationInput = {
  email?: InputMaybe<Scalars['String']>;
  profileId: Scalars['ID'];
};

export type SendReceiptDeliveryMethodsInput = {
  email?: InputMaybe<Scalars['String']>;
  phone?: InputMaybe<Scalars['String']>;
};

export type SendReceiptInput = {
  deliveryMethods?: InputMaybe<SendReceiptDeliveryMethodsInput>;
  paymentId: Scalars['ID'];
};

export type SendReceiptPayload = {
  __typename?: 'SendReceiptPayload';
  message?: Maybe<Scalars['String']>;
  success: Scalars['Boolean'];
};

export type SendgridIntegration = {
  __typename?: 'SendgridIntegration';
  apiKeyDescription?: Maybe<Scalars['String']>;
  domain: Scalars['String'];
  enabled: Scalars['Boolean'];
  id: Scalars['ID'];
  webOrigin: Scalars['String'];
};

export type SignDocumentInput = {
  documentId: Scalars['ID'];
  documentVersion: Scalars['Int'];
  latitude?: InputMaybe<Scalars['Float']>;
  longitude?: InputMaybe<Scalars['Float']>;
  /** signature image file token (4:1 aspect ratio) */
  signatureImageToken?: InputMaybe<Scalars['String']>;
  signerEmail?: InputMaybe<Scalars['String']>;
  /** patient ID if signerType is PATIENT */
  signerId?: InputMaybe<Scalars['ID']>;
  signerName: Scalars['String'];
  signerType: DocumentSignatureSignerType;
  tzid: Scalars['String'];
  verificationDetail?: InputMaybe<Scalars['String']>;
  verificationType: DocumentSignatureVerificationType;
};

export enum SortDirection {
  Asc = 'ASC',
  Desc = 'DESC'
}

export type StartAppointmentInput = {
  appointmentId: Scalars['ID'];
  startedAt?: InputMaybe<Scalars['DateTime']>;
};

export enum StateCode {
  Ak = 'AK',
  Al = 'AL',
  Ar = 'AR',
  Az = 'AZ',
  Ca = 'CA',
  Co = 'CO',
  Ct = 'CT',
  De = 'DE',
  Fl = 'FL',
  Ga = 'GA',
  Hi = 'HI',
  Ia = 'IA',
  Id = 'ID',
  Il = 'IL',
  In = 'IN',
  Ks = 'KS',
  Ky = 'KY',
  La = 'LA',
  Ma = 'MA',
  Md = 'MD',
  Me = 'ME',
  Mi = 'MI',
  Mn = 'MN',
  Mo = 'MO',
  Ms = 'MS',
  Mt = 'MT',
  Nc = 'NC',
  Nd = 'ND',
  Ne = 'NE',
  Nh = 'NH',
  Nj = 'NJ',
  Nm = 'NM',
  Nv = 'NV',
  Ny = 'NY',
  Oh = 'OH',
  Ok = 'OK',
  Or = 'OR',
  Pa = 'PA',
  Ri = 'RI',
  Sc = 'SC',
  Sd = 'SD',
  Tn = 'TN',
  Tx = 'TX',
  Ut = 'UT',
  Va = 'VA',
  Vt = 'VT',
  Wa = 'WA',
  Wi = 'WI',
  Wv = 'WV',
  Wy = 'WY'
}

export type TwilioIntegration = {
  __typename?: 'TwilioIntegration';
  appointmentRequestsEnabled: Scalars['Boolean'];
  appointmentsEnabled: Scalars['Boolean'];
  id: Scalars['ID'];
};

export type UnassignPaymentAccountInput = {
  marketplaceId?: InputMaybe<Scalars['ID']>;
  organizationId?: InputMaybe<Scalars['ID']>;
  paymentAccountId: Scalars['ID'];
};

export type UnassignProcedureProfileInput = {
  procedureProfileId: Scalars['ID'];
  profileId: Scalars['ID'];
};

export type UnassignRoleInput = {
  profileId: Scalars['ID'];
  resourceId?: InputMaybe<Scalars['ID']>;
  scope: RoleScope;
};

export type UnlockFormInput = {
  formId: Scalars['ID'];
};

export type UpdateApiKeyInput = {
  kid: Scalars['ID'];
  roleIds: Array<Scalars['ID']>;
};

export type UpdateAppointmentInput = {
  end?: InputMaybe<Scalars['DateTime']>;
  id: Scalars['ID'];
  location?: InputMaybe<Scalars['String']>;
  notes?: InputMaybe<Scalars['String']>;
  participants?: InputMaybe<Array<AppointmentParticipantInput>>;
  procedureBaseDefIds?: InputMaybe<Array<Scalars['ID']>>;
  start?: InputMaybe<Scalars['DateTime']>;
};

export type UpdateAppointmentRequestInput = {
  clientProfileIds?: InputMaybe<Array<Scalars['ID']>>;
  constraints?: InputMaybe<Array<AppointmentConstraintInput>>;
  id: Scalars['ID'];
  location?: InputMaybe<Scalars['String']>;
  notes?: InputMaybe<Scalars['String']>;
  procedureBaseDefIds?: InputMaybe<Array<Scalars['ID']>>;
};

export type UpdateAttentiveIntegrationInput = {
  apiKey?: InputMaybe<Scalars['String']>;
  appointmentBookedEnabled?: InputMaybe<Scalars['Boolean']>;
  appointmentBookedEvent?: InputMaybe<Scalars['String']>;
  appointmentCompletedEnabled?: InputMaybe<Scalars['Boolean']>;
  appointmentCompletedEvent?: InputMaybe<Scalars['String']>;
  marketplaceId: Scalars['ID'];
  qualiphyTextsEnabled?: InputMaybe<Scalars['Boolean']>;
  qualiphyTextsEvent?: InputMaybe<Scalars['String']>;
};

export type UpdateAvailabilityCoverageInput = {
  end: Scalars['DateTime'];
  id: Scalars['ID'];
  label: Scalars['String'];
  procedureProfileIds: Array<Scalars['ID']>;
  repeat?: InputMaybe<RepeatRuleInput>;
  start: Scalars['DateTime'];
  threshold: Scalars['Int'];
  tzid: Scalars['String'];
};

export type UpdateAvailabilityInput = {
  end: Scalars['DateTime'];
  id: Scalars['ID'];
  repeat?: InputMaybe<RepeatRuleInput>;
  start: Scalars['DateTime'];
};

export type UpdateCheckoutInput = {
  id: Scalars['ID'];
  items?: InputMaybe<Array<CheckoutItemInput>>;
  paymentMethod?: InputMaybe<PaymentMethodInput>;
};

export type UpdateClientProfileInput = {
  address?: InputMaybe<Scalars['String']>;
  /** yyyy-mm-dd */
  dob?: InputMaybe<Scalars['String']>;
  email?: InputMaybe<Scalars['String']>;
  familyName?: InputMaybe<Scalars['String']>;
  givenName?: InputMaybe<Scalars['String']>;
  id: Scalars['ID'];
  internalNotes?: InputMaybe<Scalars['String']>;
  phone?: InputMaybe<Scalars['String']>;
  sexAssignedAtBirth?: InputMaybe<Scalars['String']>;
  tzid?: InputMaybe<Scalars['String']>;
};

export type UpdateConfigGroupInput = {
  baseDefinitionIds?: InputMaybe<Array<Scalars['ID']>>;
  groupIds?: InputMaybe<Array<Scalars['ID']>>;
  marketplaceId: Scalars['ID'];
};

export type UpdateDiscountInput = {
  id: Scalars['ID'];
  percentage?: InputMaybe<Scalars['Int']>;
  procedureGroupIds?: InputMaybe<Array<Scalars['String']>>;
};

export type UpdateDocumentInput = {
  filename?: InputMaybe<Scalars['String']>;
  id: Scalars['ID'];
  label?: InputMaybe<Scalars['String']>;
  token?: InputMaybe<Scalars['String']>;
};

export type UpdateEmrInstanceInput = {
  id: Scalars['ID'];
  label: Scalars['String'];
};

export type UpdateFormNoteInput = {
  id: Scalars['ID'];
  note: Scalars['String'];
};

export type UpdateFormTemplateInput = {
  id: Scalars['ID'];
  items?: InputMaybe<Array<FormTemplateItemInput>>;
  name: Scalars['String'];
  parentId?: InputMaybe<Scalars['ID']>;
  type: FormType;
};

export type UpdateGeoperimeterInput = {
  id: Scalars['ID'];
  lat?: InputMaybe<Scalars['Float']>;
  lng?: InputMaybe<Scalars['Float']>;
  paths?: InputMaybe<Scalars['String']>;
  radius?: InputMaybe<Scalars['Float']>;
  travelFee?: InputMaybe<Scalars['Int']>;
  type: GeoperimeterType;
};

export type UpdateMarketplaceGroupInput = {
  id: Scalars['ID'];
  label?: InputMaybe<Scalars['String']>;
};

export type UpdateMarketplaceInput = {
  faviconToken?: InputMaybe<Scalars['String']>;
  feeProfileBasisPoints?: InputMaybe<Scalars['Int']>;
  feeProfileFixed?: InputMaybe<Scalars['Int']>;
  groupId?: InputMaybe<Scalars['ID']>;
  hasPaymentPolicy?: InputMaybe<Scalars['Boolean']>;
  id: Scalars['ID'];
  logoToken?: InputMaybe<Scalars['String']>;
  name?: InputMaybe<Scalars['String']>;
  paymentCollectionMethod?: InputMaybe<PaymentCollectionMethod>;
  paymentDepositType?: InputMaybe<PaymentDepositType>;
  paymentDepositValue?: InputMaybe<Scalars['Float']>;
  paymentPolicyName?: InputMaybe<Scalars['String']>;
  paymentPolicyText?: InputMaybe<Scalars['String']>;
  primaryColor?: InputMaybe<Scalars['String']>;
  requireDispatchApproval?: InputMaybe<Scalars['Boolean']>;
  requirePaymentPolicyAttestation?: InputMaybe<Scalars['Boolean']>;
  requirePractitionerApproval?: InputMaybe<Scalars['Boolean']>;
  reviewsIoKey?: InputMaybe<Scalars['String']>;
  reviewsIoStoreId?: InputMaybe<Scalars['String']>;
  slackWebhookUrl?: InputMaybe<Scalars['String']>;
};

export type UpdateMarketplaceUserInput = {
  clientProfile?: InputMaybe<ClientProfileInput>;
  email?: InputMaybe<Scalars['String']>;
  emailConfirmed?: InputMaybe<Scalars['Boolean']>;
  emailOptIn?: InputMaybe<Scalars['Boolean']>;
  id: Scalars['ID'];
  membership?: InputMaybe<Scalars['String']>;
  phone?: InputMaybe<Scalars['String']>;
  phoneConfirmed?: InputMaybe<Scalars['Boolean']>;
  phoneOptIn?: InputMaybe<Scalars['Boolean']>;
  primaryInstrumentId?: InputMaybe<Scalars['ID']>;
};

export type UpdateMedicationInput = {
  id: Scalars['ID'];
  name: Scalars['String'];
  unit: Scalars['String'];
};

export type UpdateMembershipDefinitionInput = {
  advertise?: InputMaybe<Scalars['Boolean']>;
  description?: InputMaybe<Scalars['String']>;
  id: Scalars['ID'];
  list?: InputMaybe<Scalars['String']>;
  name?: InputMaybe<Scalars['String']>;
  packageId?: InputMaybe<Scalars['ID']>;
  period?: InputMaybe<MembershipDefinitionPeriodType>;
  price?: InputMaybe<Scalars['Int']>;
};

export type UpdateMembershipInput = {
  id: Scalars['ID'];
  renewalDate?: InputMaybe<Scalars['DateTime']>;
  status?: InputMaybe<MembershipStatusType>;
};

export type UpdateOrderInput = {
  expiresAt: Scalars['DateTime'];
  id: Scalars['ID'];
  note?: InputMaybe<Scalars['String']>;
  procedureDefIds?: InputMaybe<Array<Scalars['ID']>>;
  refills: Scalars['Int'];
  startsAt?: InputMaybe<Scalars['DateTime']>;
};

export type UpdateOrganizationInput = {
  address?: InputMaybe<Scalars['String']>;
  email?: InputMaybe<Scalars['String']>;
  enablePractitionerSms?: InputMaybe<Scalars['Boolean']>;
  enableReceiptSending?: InputMaybe<Scalars['Boolean']>;
  googleReviewsUrl?: InputMaybe<Scalars['String']>;
  id: Scalars['ID'];
  name?: InputMaybe<Scalars['String']>;
  phone?: InputMaybe<Scalars['String']>;
  providesAtClinic?: InputMaybe<Scalars['Boolean']>;
  qualiphyApiKey?: InputMaybe<Scalars['String']>;
  slackWebhookUrl?: InputMaybe<Scalars['String']>;
  state?: InputMaybe<StateCode>;
  tzid?: InputMaybe<Scalars['String']>;
};

export type UpdatePackageInput = {
  advertise?: InputMaybe<Scalars['Boolean']>;
  description?: InputMaybe<Scalars['String']>;
  id: Scalars['ID'];
  list?: InputMaybe<Scalars['String']>;
  name?: InputMaybe<Scalars['String']>;
  price?: InputMaybe<Scalars['Int']>;
};

export type UpdatePackageItemDefinitionInput = {
  id: Scalars['ID'];
  points?: InputMaybe<Scalars['Int']>;
  procedureGroupIds?: InputMaybe<Array<Scalars['ID']>>;
};

export type UpdatePackageItemInput = {
  expiresAt?: InputMaybe<Scalars['DateTime']>;
  id: Scalars['ID'];
  primaryOrganizationId?: InputMaybe<Scalars['ID']>;
};

export type UpdatePatientInput = {
  allergies?: InputMaybe<Scalars['String']>;
  conditions?: InputMaybe<Scalars['String']>;
  id: Scalars['ID'];
  immunizations?: InputMaybe<Scalars['String']>;
  labs?: InputMaybe<Scalars['String']>;
  medications?: InputMaybe<Scalars['String']>;
  procedures?: InputMaybe<Scalars['String']>;
};

export type UpdatePaymentAccountInput = {
  enabled?: InputMaybe<Scalars['Boolean']>;
  id: Scalars['ID'];
  label: Scalars['String'];
};

export type UpdateProcedureBaseDefinitionGroupInput = {
  bannerToken?: InputMaybe<Scalars['String']>;
  baseDefinitionIds?: InputMaybe<Array<Scalars['ID']>>;
  bgcolor?: InputMaybe<Scalars['String']>;
  description?: InputMaybe<Scalars['String']>;
  featuredBaseDefIds?: InputMaybe<Array<Scalars['ID']>>;
  fontColor?: InputMaybe<Scalars['String']>;
  groupIds?: InputMaybe<Array<Scalars['ID']>>;
  id: Scalars['ID'];
  name?: InputMaybe<Scalars['String']>;
  thumbnailToken?: InputMaybe<Scalars['String']>;
};

export type UpdateProcedureBaseDefinitionInput = {
  /** procedure base def group ids */
  addOnIds?: InputMaybe<Array<Scalars['ID']>>;
  category?: InputMaybe<Scalars['String']>;
  description: Scalars['String'];
  duration: Scalars['Float'];
  id: Scalars['ID'];
  ingredients?: InputMaybe<Scalars['String']>;
  layouts?: InputMaybe<Array<ProcedureBaseDefLayoutInput>>;
  name: Scalars['String'];
  points?: InputMaybe<Scalars['Float']>;
  tagline?: InputMaybe<Scalars['String']>;
  thumbnailToken?: InputMaybe<Scalars['String']>;
};

export type UpdateProcedureDefinitionInput = {
  assessmentFormTemplateId?: InputMaybe<Scalars['ID']>;
  baseDefinitionIds?: InputMaybe<Array<Scalars['ID']>>;
  consentFormIds?: InputMaybe<Array<Scalars['ID']>>;
  description: Scalars['String'];
  duration: Scalars['Float'];
  id: Scalars['ID'];
  interventionFormTemplateId?: InputMaybe<Scalars['ID']>;
  medicationProtocols?: InputMaybe<Array<MedicationProtocolInput>>;
  name: Scalars['String'];
  price: Scalars['Float'];
};

export type UpdateProcedureProfileInput = {
  id: Scalars['ID'];
  name?: InputMaybe<Scalars['String']>;
  procedureDefIds?: InputMaybe<Array<Scalars['ID']>>;
};

export type UpdateProfileInput = {
  address?: InputMaybe<Scalars['String']>;
  allowSmsNotifications?: InputMaybe<Scalars['Boolean']>;
  availableUntil?: InputMaybe<Scalars['DateTime']>;
  color?: InputMaybe<Scalars['String']>;
  email?: InputMaybe<Scalars['String']>;
  familyName?: InputMaybe<Scalars['String']>;
  givenName?: InputMaybe<Scalars['String']>;
  id: Scalars['ID'];
  phone?: InputMaybe<Scalars['String']>;
  title?: InputMaybe<Scalars['String']>;
  tzid?: InputMaybe<Scalars['String']>;
};

export type UpdatePromoCodeInput = {
  activationEndDate?: InputMaybe<Scalars['String']>;
  activationStartDate?: InputMaybe<Scalars['String']>;
  active?: InputMaybe<Scalars['Boolean']>;
  code?: InputMaybe<Scalars['String']>;
  discountType?: InputMaybe<PromoCodeDiscountType>;
  discountValue?: InputMaybe<Scalars['Int']>;
  id: Scalars['ID'];
  minimumBookingAmount?: InputMaybe<Scalars['Int']>;
  name?: InputMaybe<Scalars['String']>;
  organizationIds?: InputMaybe<Array<Scalars['String']>>;
  procedureGroupIds?: InputMaybe<Array<Scalars['String']>>;
  usageEndDate?: InputMaybe<Scalars['String']>;
  usageLimitPerUser?: InputMaybe<Scalars['Int']>;
  usageLimitTotal?: InputMaybe<Scalars['Int']>;
  usageStartDate?: InputMaybe<Scalars['String']>;
};

export type UpdateQualiphyExamInput = {
  expiresAfter?: InputMaybe<Scalars['Int']>;
  id: Scalars['ID'];
  organizationId: Scalars['ID'];
  procedureDefinitionIds: Array<Scalars['ID']>;
  refills?: InputMaybe<Scalars['Int']>;
};

export type UpdateQualiphyIntegrationInput = {
  apiKey?: InputMaybe<Scalars['String']>;
  enabled?: InputMaybe<Scalars['Boolean']>;
  examIds?: InputMaybe<Array<Scalars['String']>>;
  organizationId: Scalars['ID'];
};

export type UpdateRoleInput = {
  id: Scalars['ID'];
  name?: InputMaybe<Scalars['String']>;
  permissions?: InputMaybe<Array<Scalars['String']>>;
};

export type UpdateSegmentIntegrationInput = {
  enabled?: InputMaybe<Scalars['Boolean']>;
  marketplaceId: Scalars['ID'];
  writeKey?: InputMaybe<Scalars['String']>;
};

export type UpdateSendgridIntegrationInput = {
  apiKey?: InputMaybe<Scalars['String']>;
  domain?: InputMaybe<Scalars['String']>;
  enabled?: InputMaybe<Scalars['Boolean']>;
  marketplaceId: Scalars['ID'];
  webOrigin?: InputMaybe<Scalars['String']>;
};

export type UpdateTwilioIntegrationInput = {
  appointmentRequestsEnabled?: InputMaybe<Scalars['Boolean']>;
  appointmentsEnabled?: InputMaybe<Scalars['Boolean']>;
  marketplaceId: Scalars['ID'];
};

export type UsePaymentAccountInput = {
  marketplaceId?: InputMaybe<Scalars['ID']>;
  organizationId?: InputMaybe<Scalars['ID']>;
  paymentAccountId?: InputMaybe<Scalars['ID']>;
};

export type User = {
  __typename?: 'User';
  apiKeys: Array<ApiKey>;
  createdAt: Scalars['DateTime'];
  email: Scalars['String'];
  emailConfirmed: Scalars['Boolean'];
  id: Scalars['ID'];
  profiles: Array<Profile>;
  updatedAt: Scalars['DateTime'];
};

export type ValidatePromoCodeInput = {
  bookingAmount?: InputMaybe<Scalars['Int']>;
  bookingDate?: InputMaybe<Scalars['String']>;
  code: Scalars['String'];
  marketplaceId: Scalars['ID'];
  marketplaceUserId?: InputMaybe<Scalars['ID']>;
  organizationId?: InputMaybe<Scalars['ID']>;
  procedureBaseDefIds?: InputMaybe<Array<Scalars['String']>>;
};

export type VerifyEmailInput = {
  token: Scalars['String'];
};

export type VitalSign = {
  __typename?: 'VitalSign';
  createdAt: Scalars['DateTime'];
  createdById: Scalars['ID'];
  id: Scalars['ID'];
  recordedAt: Scalars['DateTime'];
  type: VitalSignType;
  updatedAt: Scalars['DateTime'];
  value: Scalars['String'];
};

export type VitalSignInput = {
  type: VitalSignType;
  value: Scalars['String'];
};

export enum VitalSignType {
  BloodPressure = 'BLOOD_PRESSURE',
  OxygenSaturation = 'OXYGEN_SATURATION',
  Pulse = 'PULSE',
  RespiratoryRate = 'RESPIRATORY_RATE',
  Temperature = 'TEMPERATURE'
}

export type AppointmentRequestFieldsFragment = { __typename?: 'AppointmentRequest', id: string, location: string, status: AppointmentRequestStatus, procedureBaseDefs: Array<{ __typename?: 'ProcedureBaseDefinition', id: string, marketplaceId: string, duration: number }>, constraints: Array<{ __typename?: 'AppointmentConstraint', timeRanges: Array<{ __typename?: 'AppointmentTimeRange', start: any, end: any }>, organizations: Array<{ __typename?: 'Organization', id: string, name: string, phone?: string | null, address?: string | null, providesAtClinic: boolean, geoperimeters: Array<{ __typename?: 'Geoperimeter', id: string, type: GeoperimeterType, lat?: number | null, lng?: number | null, radius?: number | null, paths?: string | null, travelFee?: number | null }>, procedureDefs: Array<{ __typename?: 'ProcedureDefinition', id: string, name: string, description: string, price: number, organizationId: string, duration: number, baseDefinitions: Array<{ __typename?: 'ProcedureBaseDefinition', id: string, marketplaceId: string }> }> }> }> };

export type ConstraintFieldsFragment = { __typename?: 'AppointmentConstraint', timeRanges: Array<{ __typename?: 'AppointmentTimeRange', start: any, end: any }>, organizations: Array<{ __typename?: 'Organization', id: string, name: string, phone?: string | null, address?: string | null, providesAtClinic: boolean, geoperimeters: Array<{ __typename?: 'Geoperimeter', id: string, type: GeoperimeterType, lat?: number | null, lng?: number | null, radius?: number | null, paths?: string | null, travelFee?: number | null }>, procedureDefs: Array<{ __typename?: 'ProcedureDefinition', id: string, name: string, description: string, price: number, organizationId: string, duration: number, baseDefinitions: Array<{ __typename?: 'ProcedureBaseDefinition', id: string, marketplaceId: string }> }> }> };

export type PractitionerParticipantFieldsFragment = { __typename?: 'Profile', title: string, givenName: string, familyName: string, organization?: { __typename?: 'Organization', phone?: string | null } | null };

export type ProfileAppointmentFieldsFragment = { __typename?: 'Appointment', location: string, startedAt?: any | null, completedAt?: any | null, status: AppointmentStatus, id: string, start: any, end: any, duration: number, participants: Array<{ __typename?: 'AppointmentParticipant', type: ParticipantType, participant?: { __typename?: 'ClientProfile' } | { __typename?: 'Profile', title: string, givenName: string, familyName: string, organization?: { __typename?: 'Organization', phone?: string | null } | null } | null }>, procedureBaseDefs: Array<{ __typename?: 'ProcedureBaseDefinition', id: string, marketplaceId: string, duration: number }> };

export type CancelAppointmentMutationVariables = Exact<{
  input: CancelAppointmentInput;
}>;


export type CancelAppointmentMutation = { __typename?: 'Mutation', cancelAppointment?: { __typename?: 'Appointment', id: string } | null };

export type CreateAppointmentRequestMutationVariables = Exact<{
  input: CreateAppointmentRequestInput;
}>;


export type CreateAppointmentRequestMutation = { __typename?: 'Mutation', createAppointmentRequest?: { __typename?: 'AppointmentRequest', id: string, location: string, status: AppointmentRequestStatus, procedureBaseDefs: Array<{ __typename?: 'ProcedureBaseDefinition', id: string, marketplaceId: string, duration: number }>, constraints: Array<{ __typename?: 'AppointmentConstraint', timeRanges: Array<{ __typename?: 'AppointmentTimeRange', start: any, end: any }>, organizations: Array<{ __typename?: 'Organization', id: string, name: string, phone?: string | null, address?: string | null, providesAtClinic: boolean, geoperimeters: Array<{ __typename?: 'Geoperimeter', id: string, type: GeoperimeterType, lat?: number | null, lng?: number | null, radius?: number | null, paths?: string | null, travelFee?: number | null }>, procedureDefs: Array<{ __typename?: 'ProcedureDefinition', id: string, name: string, description: string, price: number, organizationId: string, duration: number, baseDefinitions: Array<{ __typename?: 'ProcedureBaseDefinition', id: string, marketplaceId: string }> }> }> }> } | null };

export type ProfileAppointmentsAndRequestsQueryVariables = Exact<{
  id: Scalars['ID'];
}>;


export type ProfileAppointmentsAndRequestsQuery = { __typename?: 'Query', marketplaceUser: { __typename?: 'MarketplaceUser', appointments: Array<{ __typename?: 'Appointment', location: string, startedAt?: any | null, completedAt?: any | null, status: AppointmentStatus, id: string, start: any, end: any, duration: number, participants: Array<{ __typename?: 'AppointmentParticipant', type: ParticipantType, participant?: { __typename?: 'ClientProfile' } | { __typename?: 'Profile', title: string, givenName: string, familyName: string, organization?: { __typename?: 'Organization', phone?: string | null } | null } | null }>, procedureBaseDefs: Array<{ __typename?: 'ProcedureBaseDefinition', id: string, marketplaceId: string, duration: number }> }>, appointmentRequests: Array<{ __typename?: 'AppointmentRequest', id: string, location: string, status: AppointmentRequestStatus, procedureBaseDefs: Array<{ __typename?: 'ProcedureBaseDefinition', id: string, marketplaceId: string, duration: number }>, constraints: Array<{ __typename?: 'AppointmentConstraint', timeRanges: Array<{ __typename?: 'AppointmentTimeRange', start: any, end: any }>, organizations: Array<{ __typename?: 'Organization', id: string, name: string, phone?: string | null, address?: string | null, providesAtClinic: boolean, geoperimeters: Array<{ __typename?: 'Geoperimeter', id: string, type: GeoperimeterType, lat?: number | null, lng?: number | null, radius?: number | null, paths?: string | null, travelFee?: number | null }>, procedureDefs: Array<{ __typename?: 'ProcedureDefinition', id: string, name: string, description: string, price: number, organizationId: string, duration: number, baseDefinitions: Array<{ __typename?: 'ProcedureBaseDefinition', id: string, marketplaceId: string }> }> }> }> }> } };

export type AppointmentFieldsFragment = { __typename?: 'Appointment', id: string, start: any, end: any, duration: number };

export type AvailabilityFieldsFragment = { __typename?: 'Availability', type: AvailabilityType, start: any, end: any, duration: number, tzid: string, repeat?: { __typename?: 'RepeatRule', id: string, frequency: RepeatFrequencyType, byWeekday: Array<RepeatWeekdayType> } | null };

export type DiscountFieldsFragment = { __typename?: 'Discount', id: string, percentage: number, membershipDefinitionId: string, procedureGroups: Array<{ __typename?: 'ProcedureBaseDefinitionGroup', id: string, name: string }> };

export type FullMarketplaceFieldsFragment = { __typename?: 'Marketplace', id: string, name: string, groupId: string, createdAt: any, navigationGroupId?: string | null, logo?: string | null, favicon?: string | null, primaryColor?: string | null, paymentCollectionMethod?: PaymentCollectionMethod | null, paymentDepositType?: PaymentDepositType | null, paymentDepositValue?: number | null, hasPaymentPolicy?: boolean | null, paymentPolicyName?: string | null, paymentPolicyText?: string | null, requirePaymentPolicyAttestation?: boolean | null, procedureBaseDefs: Array<{ __typename?: 'ProcedureBaseDefinition', id: string, marketplaceId: string, name: string, description: string, duration: number, thumbnail?: string | null, tags: Array<string>, addOns: Array<string>, tagline?: string | null, category?: string | null, ingredients?: string | null }>, organizations: Array<{ __typename?: 'Organization', id: string, name: string, phone?: string | null, address?: string | null, providesAtClinic: boolean, geoperimeters: Array<{ __typename?: 'Geoperimeter', id: string, type: GeoperimeterType, lat?: number | null, lng?: number | null, radius?: number | null, paths?: string | null, travelFee?: number | null }>, procedureDefs: Array<{ __typename?: 'ProcedureDefinition', id: string, name: string, description: string, price: number, organizationId: string, duration: number, baseDefinitions: Array<{ __typename?: 'ProcedureBaseDefinition', id: string, marketplaceId: string }> }> }>, procedureBaseDefGroups: Array<{ __typename?: 'ProcedureBaseDefinitionGroup', id: string, name: string, thumbnail?: string | null, banner?: string | null, description?: string | null, bgcolor?: string | null, fontColor?: string | null, procedureBaseDefs: Array<{ __typename?: 'ProcedureBaseDefinition', id: string, name: string, tagline?: string | null, thumbnail?: string | null }>, procedureBaseDefGroups: Array<{ __typename?: 'ProcedureBaseDefinitionGroup', id: string, name: string, thumbnail?: string | null }>, featuredBaseDefs: Array<{ __typename?: 'ProcedureBaseDefinition', id: string, name: string, thumbnail?: string | null }> }>, membershipDefinitions: Array<{ __typename?: 'MembershipDefinition', id: string, name: string, price: number, period: MembershipDefinitionPeriodType, packageId?: string | null, marketplaceId: string, advertise: boolean, description?: string | null, list?: string | null, discounts: Array<{ __typename?: 'Discount', id: string, percentage: number, membershipDefinitionId: string, procedureGroups: Array<{ __typename?: 'ProcedureBaseDefinitionGroup', id: string, name: string }> }> }>, packages: Array<{ __typename?: 'Package', id: string, name: string, price: number, marketplaceId: string, advertise: boolean, description?: string | null, list?: string | null, packageItemDefinitions: Array<{ __typename?: 'PackageItemDefinition', id: string, packageId: string, points: number, procedureGroups: Array<string> }> }> };

export type FullProcedureBaseDefFieldsFragment = { __typename?: 'ProcedureBaseDefinition', id: string, marketplaceId: string, name: string, description: string, duration: number, thumbnail?: string | null, tags: Array<string>, addOns: Array<string>, tagline?: string | null, category?: string | null, ingredients?: string | null, layouts: Array<{ __typename?: 'ProcedureBaseDefinitionLayout', id: string, type: ProcedureBaseDefinitionLayoutType, layout: string, itemIndex: number }> };

export type FullProfileFieldsFragment = { __typename?: 'Profile', availableUntil?: any | null, id: string, availabilities: Array<{ __typename?: 'Availability', type: AvailabilityType, start: any, end: any, duration: number, tzid: string, repeat?: { __typename?: 'RepeatRule', id: string, frequency: RepeatFrequencyType, byWeekday: Array<RepeatWeekdayType> } | null }>, procedureProfiles: Array<{ __typename?: 'ProcedureProfile', procedureDefs: Array<{ __typename?: 'ProcedureDefinition', baseDefinitions: Array<{ __typename?: 'ProcedureBaseDefinition', id: string }> }> }> };

export type GeoperimeterFieldsFragment = { __typename?: 'Geoperimeter', id: string, type: GeoperimeterType, lat?: number | null, lng?: number | null, radius?: number | null, paths?: string | null, travelFee?: number | null };

export type MarketplaceFieldsFragment = { __typename?: 'Marketplace', id: string, name: string, groupId: string, createdAt: any, navigationGroupId?: string | null, logo?: string | null, favicon?: string | null, primaryColor?: string | null, paymentCollectionMethod?: PaymentCollectionMethod | null, paymentDepositType?: PaymentDepositType | null, paymentDepositValue?: number | null, hasPaymentPolicy?: boolean | null, paymentPolicyName?: string | null, paymentPolicyText?: string | null, requirePaymentPolicyAttestation?: boolean | null };

export type MarketplaceGroupFieldsFragment = { __typename?: 'MarketplaceGroup', id: string, label: string, createdAt: any };

export type MembershipDefinitionFieldsFragment = { __typename?: 'MembershipDefinition', id: string, name: string, price: number, period: MembershipDefinitionPeriodType, packageId?: string | null, marketplaceId: string, advertise: boolean, description?: string | null, list?: string | null, discounts: Array<{ __typename?: 'Discount', id: string, percentage: number, membershipDefinitionId: string, procedureGroups: Array<{ __typename?: 'ProcedureBaseDefinitionGroup', id: string, name: string }> }> };

export type OrganizationFieldsFragment = { __typename?: 'Organization', id: string, name: string, phone?: string | null, address?: string | null, providesAtClinic: boolean, geoperimeters: Array<{ __typename?: 'Geoperimeter', id: string, type: GeoperimeterType, lat?: number | null, lng?: number | null, radius?: number | null, paths?: string | null, travelFee?: number | null }>, procedureDefs: Array<{ __typename?: 'ProcedureDefinition', id: string, name: string, description: string, price: number, organizationId: string, duration: number, baseDefinitions: Array<{ __typename?: 'ProcedureBaseDefinition', id: string, marketplaceId: string }> }> };

export type PackageFieldsFragment = { __typename?: 'Package', id: string, name: string, price: number, marketplaceId: string, advertise: boolean, description?: string | null, list?: string | null, packageItemDefinitions: Array<{ __typename?: 'PackageItemDefinition', id: string, packageId: string, points: number, procedureGroups: Array<string> }> };

export type PackageItemDefinitionFieldsFragment = { __typename?: 'PackageItemDefinition', id: string, packageId: string, points: number, procedureGroups: Array<string> };

export type ProcedureBaseDefFieldsFragment = { __typename?: 'ProcedureBaseDefinition', id: string, marketplaceId: string, name: string, description: string, duration: number, thumbnail?: string | null, tags: Array<string>, addOns: Array<string>, tagline?: string | null, category?: string | null, ingredients?: string | null };

export type ProcedureBaseDefGroupFieldsFragment = { __typename?: 'ProcedureBaseDefinitionGroup', id: string, name: string, thumbnail?: string | null, banner?: string | null, description?: string | null, bgcolor?: string | null, fontColor?: string | null, procedureBaseDefs: Array<{ __typename?: 'ProcedureBaseDefinition', id: string, name: string, tagline?: string | null, thumbnail?: string | null }>, procedureBaseDefGroups: Array<{ __typename?: 'ProcedureBaseDefinitionGroup', id: string, name: string, thumbnail?: string | null }>, featuredBaseDefs: Array<{ __typename?: 'ProcedureBaseDefinition', id: string, name: string, thumbnail?: string | null }> };

export type ProcedureBaseDefLayoutFieldsFragment = { __typename?: 'ProcedureBaseDefinitionLayout', id: string, type: ProcedureBaseDefinitionLayoutType, layout: string, itemIndex: number };

export type ProcedureDefFieldsFragment = { __typename?: 'ProcedureDefinition', id: string, name: string, description: string, price: number, organizationId: string, duration: number };

export type ProfileFieldsFragment = { __typename?: 'Profile', id: string };

export type MarketplaceQueryVariables = Exact<{
  id: Scalars['ID'];
}>;


export type MarketplaceQuery = { __typename?: 'Query', marketplace?: { __typename?: 'Marketplace', id: string, name: string, groupId: string, createdAt: any, navigationGroupId?: string | null, logo?: string | null, favicon?: string | null, primaryColor?: string | null, paymentCollectionMethod?: PaymentCollectionMethod | null, paymentDepositType?: PaymentDepositType | null, paymentDepositValue?: number | null, hasPaymentPolicy?: boolean | null, paymentPolicyName?: string | null, paymentPolicyText?: string | null, requirePaymentPolicyAttestation?: boolean | null, procedureBaseDefs: Array<{ __typename?: 'ProcedureBaseDefinition', id: string, marketplaceId: string, name: string, description: string, duration: number, thumbnail?: string | null, tags: Array<string>, addOns: Array<string>, tagline?: string | null, category?: string | null, ingredients?: string | null }>, organizations: Array<{ __typename?: 'Organization', id: string, name: string, phone?: string | null, address?: string | null, providesAtClinic: boolean, geoperimeters: Array<{ __typename?: 'Geoperimeter', id: string, type: GeoperimeterType, lat?: number | null, lng?: number | null, radius?: number | null, paths?: string | null, travelFee?: number | null }>, procedureDefs: Array<{ __typename?: 'ProcedureDefinition', id: string, name: string, description: string, price: number, organizationId: string, duration: number, baseDefinitions: Array<{ __typename?: 'ProcedureBaseDefinition', id: string, marketplaceId: string }> }> }>, procedureBaseDefGroups: Array<{ __typename?: 'ProcedureBaseDefinitionGroup', id: string, name: string, thumbnail?: string | null, banner?: string | null, description?: string | null, bgcolor?: string | null, fontColor?: string | null, procedureBaseDefs: Array<{ __typename?: 'ProcedureBaseDefinition', id: string, name: string, tagline?: string | null, thumbnail?: string | null }>, procedureBaseDefGroups: Array<{ __typename?: 'ProcedureBaseDefinitionGroup', id: string, name: string, thumbnail?: string | null }>, featuredBaseDefs: Array<{ __typename?: 'ProcedureBaseDefinition', id: string, name: string, thumbnail?: string | null }> }>, membershipDefinitions: Array<{ __typename?: 'MembershipDefinition', id: string, name: string, price: number, period: MembershipDefinitionPeriodType, packageId?: string | null, marketplaceId: string, advertise: boolean, description?: string | null, list?: string | null, discounts: Array<{ __typename?: 'Discount', id: string, percentage: number, membershipDefinitionId: string, procedureGroups: Array<{ __typename?: 'ProcedureBaseDefinitionGroup', id: string, name: string }> }> }>, packages: Array<{ __typename?: 'Package', id: string, name: string, price: number, marketplaceId: string, advertise: boolean, description?: string | null, list?: string | null, packageItemDefinitions: Array<{ __typename?: 'PackageItemDefinition', id: string, packageId: string, points: number, procedureGroups: Array<string> }> }> } | null };

export type MarketplaceGroupQueryVariables = Exact<{
  id: Scalars['ID'];
}>;


export type MarketplaceGroupQuery = { __typename?: 'Query', marketplaceGroup?: { __typename?: 'MarketplaceGroup', id: string, label: string, createdAt: any } | null };

export type MarketplacesQueryVariables = Exact<{ [key: string]: never; }>;


export type MarketplacesQuery = { __typename?: 'Query', marketplaces: Array<{ __typename?: 'Marketplace', id: string, name: string, groupId: string, createdAt: any, navigationGroupId?: string | null, logo?: string | null, favicon?: string | null, primaryColor?: string | null, paymentCollectionMethod?: PaymentCollectionMethod | null, paymentDepositType?: PaymentDepositType | null, paymentDepositValue?: number | null, hasPaymentPolicy?: boolean | null, paymentPolicyName?: string | null, paymentPolicyText?: string | null, requirePaymentPolicyAttestation?: boolean | null }> };

export type OrganizationAvailabilitiesQueryVariables = Exact<{
  id: Scalars['ID'];
  page?: InputMaybe<AppointmentPageInput>;
}>;


export type OrganizationAvailabilitiesQuery = { __typename?: 'Query', organization?: { __typename?: 'Organization', profiles: Array<{ __typename?: 'Profile', availableUntil?: any | null, id: string, appointments: { __typename?: 'AppointmentPage', data: Array<{ __typename?: 'Appointment', id: string, start: any, end: any, duration: number }> }, availabilities: Array<{ __typename?: 'Availability', type: AvailabilityType, start: any, end: any, duration: number, tzid: string, repeat?: { __typename?: 'RepeatRule', id: string, frequency: RepeatFrequencyType, byWeekday: Array<RepeatWeekdayType> } | null }>, procedureProfiles: Array<{ __typename?: 'ProcedureProfile', procedureDefs: Array<{ __typename?: 'ProcedureDefinition', baseDefinitions: Array<{ __typename?: 'ProcedureBaseDefinition', id: string }> }> }> }> } | null };

export type ProcedureBaseDefinitionQueryVariables = Exact<{
  id: Scalars['ID'];
}>;


export type ProcedureBaseDefinitionQuery = { __typename?: 'Query', procedureBaseDefinition?: { __typename?: 'ProcedureBaseDefinition', id: string, marketplaceId: string, name: string, description: string, duration: number, thumbnail?: string | null, tags: Array<string>, addOns: Array<string>, tagline?: string | null, category?: string | null, ingredients?: string | null, layouts: Array<{ __typename?: 'ProcedureBaseDefinitionLayout', id: string, type: ProcedureBaseDefinitionLayoutType, layout: string, itemIndex: number }> } | null };

export type PaymentInstrumentFieldsFragment = { __typename?: 'PaymentInstrument', id: string, type: PaymentInstrumentType, processor: PaymentAccountPlatform, brand?: string | null, lastFour?: string | null, expirationMonth?: number | null, expirationYear?: number | null };

export type CreateApplePayInstrumentMutationVariables = Exact<{
  input: PaymentMethodInput;
}>;


export type CreateApplePayInstrumentMutation = { __typename?: 'Mutation', createApplePayInstrument?: { __typename?: 'PaymentInstrument', id: string, type: PaymentInstrumentType, processor: PaymentAccountPlatform, brand?: string | null, lastFour?: string | null, expirationMonth?: number | null, expirationYear?: number | null } | null };

export type CreateApplePaySessionMutationVariables = Exact<{
  input: CreateApplePaySessionInput;
}>;


export type CreateApplePaySessionMutation = { __typename?: 'Mutation', createApplePaySession?: string | null };

export type CreatePaymentInstrumentMutationVariables = Exact<{
  input: PaymentMethodInput;
}>;


export type CreatePaymentInstrumentMutation = { __typename?: 'Mutation', createPaymentInstrument?: { __typename?: 'PaymentInstrument', id: string, type: PaymentInstrumentType, processor: PaymentAccountPlatform, brand?: string | null, lastFour?: string | null, expirationMonth?: number | null, expirationYear?: number | null } | null };

export type DeletePaymentInstrumentMutationVariables = Exact<{
  id: Scalars['ID'];
}>;


export type DeletePaymentInstrumentMutation = { __typename?: 'Mutation', deletePaymentInstrument?: boolean | null };

export type PreviewCheckoutItemsQueryVariables = Exact<{
  input: PreviewCheckoutItemsInput;
}>;


export type PreviewCheckoutItemsQuery = { __typename?: 'Query', previewCheckoutItems: Array<Array<{ __typename?: 'CheckoutItem', quantity: number, price: number, type: CheckoutItemType, description: string }>> };

export type ValidatePromoCodeQueryVariables = Exact<{
  input: ValidatePromoCodeInput;
}>;


export type ValidatePromoCodeQuery = { __typename?: 'Query', validatePromoCode: { __typename?: 'PromoCodeValidationResult', valid: boolean, error?: string | null, discountAmount?: number | null } };

export type ClientProfileFieldsFragment = { __typename?: 'ClientProfile', id: string, email: string, familyName: string, givenName: string, phone: string, address: string, dob: string, tzid: string, sexAssignedAtBirth?: string | null, createdAt: any, updatedAt: any };

export type MarketplaceUserFieldsFragment = { __typename?: 'MarketplaceUser', id: string, clientProfileId: string, createdAt: any, updatedAt: any, primaryInstrumentId?: string | null, email?: string | null, emailConfirmed: boolean, phone?: string | null, phoneConfirmed: boolean, memberships: Array<{ __typename?: 'Membership', id: string, membershipDefinitionId: string, renewalDate: any, name: string, status: MembershipStatusType }>, packageItems: Array<{ __typename?: 'PackageItem', id: string, packageItemDefinitionId: string, balance: number, packageName: string, expiresAt?: any | null, totalPoints: number, membershipId?: string | null, groupNames: string }>, paymentInstruments: Array<{ __typename?: 'PaymentInstrument', id: string, type: PaymentInstrumentType, processor: PaymentAccountPlatform, brand?: string | null, lastFour?: string | null, expirationMonth?: number | null, expirationYear?: number | null }> };

export type MembershipFieldsFragment = { __typename?: 'Membership', id: string, membershipDefinitionId: string, renewalDate: any, name: string, status: MembershipStatusType };

export type PackageItemFieldsFragment = { __typename?: 'PackageItem', id: string, packageItemDefinitionId: string, balance: number, packageName: string, expiresAt?: any | null, totalPoints: number, membershipId?: string | null, groupNames: string };

export type CancelMembershipMutationVariables = Exact<{
  id: Scalars['ID'];
}>;


export type CancelMembershipMutation = { __typename?: 'Mutation', cancelMembership?: boolean | null };

export type ConfirmConsumerEmailMutationVariables = Exact<{
  input: ConfirmConsumerEmailInput;
}>;


export type ConfirmConsumerEmailMutation = { __typename?: 'Mutation', confirmConsumerEmail: boolean };

export type ConfirmConsumerPhoneMutationVariables = Exact<{
  input: ConfirmConsumerPhoneInput;
}>;


export type ConfirmConsumerPhoneMutation = { __typename?: 'Mutation', confirmConsumerPhone: boolean };

export type CreateMarketplaceUserMutationVariables = Exact<{
  input: CreateMarketplaceUserInput;
}>;


export type CreateMarketplaceUserMutation = { __typename?: 'Mutation', createMarketplaceUser?: { __typename?: 'MarketplaceUser', id: string, clientProfileId: string, createdAt: any, updatedAt: any, primaryInstrumentId?: string | null, email?: string | null, emailConfirmed: boolean, phone?: string | null, phoneConfirmed: boolean, memberships: Array<{ __typename?: 'Membership', id: string, membershipDefinitionId: string, renewalDate: any, name: string, status: MembershipStatusType }>, packageItems: Array<{ __typename?: 'PackageItem', id: string, packageItemDefinitionId: string, balance: number, packageName: string, expiresAt?: any | null, totalPoints: number, membershipId?: string | null, groupNames: string }>, paymentInstruments: Array<{ __typename?: 'PaymentInstrument', id: string, type: PaymentInstrumentType, processor: PaymentAccountPlatform, brand?: string | null, lastFour?: string | null, expirationMonth?: number | null, expirationYear?: number | null }> } | null };

export type CreateMembershipMutationVariables = Exact<{
  input: CreateMembershipInput;
}>;


export type CreateMembershipMutation = { __typename?: 'Mutation', createMembership?: { __typename?: 'Membership', id: string, membershipDefinitionId: string, renewalDate: any, name: string, status: MembershipStatusType } | null };

export type GrantPackageMutationVariables = Exact<{
  input: GrantPackageInput;
}>;


export type GrantPackageMutation = { __typename?: 'Mutation', grantPackage?: Array<{ __typename?: 'PackageItem', id: string, packageItemDefinitionId: string, balance: number, packageName: string, expiresAt?: any | null, totalPoints: number, membershipId?: string | null, groupNames: string }> | null };

export type ReactivateMembershipMutationVariables = Exact<{
  id: Scalars['ID'];
}>;


export type ReactivateMembershipMutation = { __typename?: 'Mutation', reactivateMembership?: { __typename?: 'Membership', id: string, membershipDefinitionId: string, renewalDate: any, name: string, status: MembershipStatusType } | null };

export type RequestConfirmConsumerEmailMutationVariables = Exact<{
  input: RequestConfirmConsumerEmailInput;
}>;


export type RequestConfirmConsumerEmailMutation = { __typename?: 'Mutation', requestConfirmConsumerEmail: boolean };

export type RequestConfirmConsumerPhoneMutationVariables = Exact<{
  input: RequestConfirmConsumerPhoneInput;
}>;


export type RequestConfirmConsumerPhoneMutation = { __typename?: 'Mutation', requestConfirmConsumerPhone: boolean };

export type UpdateClientProfileMutationVariables = Exact<{
  input: UpdateClientProfileInput;
}>;


export type UpdateClientProfileMutation = { __typename?: 'Mutation', updateClientProfile?: { __typename?: 'ClientProfile', id: string, email: string, familyName: string, givenName: string, phone: string, address: string, dob: string, tzid: string, sexAssignedAtBirth?: string | null, createdAt: any, updatedAt: any } | null };

export type UpdateMarketplaceUserMutationVariables = Exact<{
  input: UpdateMarketplaceUserInput;
}>;


export type UpdateMarketplaceUserMutation = { __typename?: 'Mutation', updateMarketplaceUser?: { __typename?: 'MarketplaceUser', id: string, clientProfileId: string, createdAt: any, updatedAt: any, primaryInstrumentId?: string | null, email?: string | null, emailConfirmed: boolean, phone?: string | null, phoneConfirmed: boolean, memberships: Array<{ __typename?: 'Membership', id: string, membershipDefinitionId: string, renewalDate: any, name: string, status: MembershipStatusType }>, packageItems: Array<{ __typename?: 'PackageItem', id: string, packageItemDefinitionId: string, balance: number, packageName: string, expiresAt?: any | null, totalPoints: number, membershipId?: string | null, groupNames: string }>, paymentInstruments: Array<{ __typename?: 'PaymentInstrument', id: string, type: PaymentInstrumentType, processor: PaymentAccountPlatform, brand?: string | null, lastFour?: string | null, expirationMonth?: number | null, expirationYear?: number | null }> } | null };

export type MarketplaceUserQueryVariables = Exact<{
  id: Scalars['ID'];
}>;


export type MarketplaceUserQuery = { __typename?: 'Query', marketplaceUser: { __typename?: 'MarketplaceUser', id: string, clientProfileId: string, createdAt: any, updatedAt: any, primaryInstrumentId?: string | null, email?: string | null, emailConfirmed: boolean, phone?: string | null, phoneConfirmed: boolean, clientProfile: { __typename?: 'ClientProfile', id: string, email: string, familyName: string, givenName: string, phone: string, address: string, dob: string, tzid: string, sexAssignedAtBirth?: string | null, createdAt: any, updatedAt: any }, memberships: Array<{ __typename?: 'Membership', id: string, membershipDefinitionId: string, renewalDate: any, name: string, status: MembershipStatusType }>, packageItems: Array<{ __typename?: 'PackageItem', id: string, packageItemDefinitionId: string, balance: number, packageName: string, expiresAt?: any | null, totalPoints: number, membershipId?: string | null, groupNames: string }>, paymentInstruments: Array<{ __typename?: 'PaymentInstrument', id: string, type: PaymentInstrumentType, processor: PaymentAccountPlatform, brand?: string | null, lastFour?: string | null, expirationMonth?: number | null, expirationYear?: number | null }> } };

export const GeoperimeterFieldsFragmentDoc = gql`
    fragment GeoperimeterFields on Geoperimeter {
  id
  type
  lat
  lng
  radius
  paths
  travelFee
}
    `;
export const ProcedureDefFieldsFragmentDoc = gql`
    fragment ProcedureDefFields on ProcedureDefinition {
  id
  name
  description
  price
  organizationId
  duration
}
    `;
export const OrganizationFieldsFragmentDoc = gql`
    fragment OrganizationFields on Organization {
  id
  name
  phone
  address
  providesAtClinic
  geoperimeters {
    ...GeoperimeterFields
  }
  procedureDefs {
    ...ProcedureDefFields
    baseDefinitions {
      id
      marketplaceId
    }
  }
}
    ${GeoperimeterFieldsFragmentDoc}
${ProcedureDefFieldsFragmentDoc}`;
export const ConstraintFieldsFragmentDoc = gql`
    fragment ConstraintFields on AppointmentConstraint {
  timeRanges {
    start
    end
  }
  organizations {
    ...OrganizationFields
  }
}
    ${OrganizationFieldsFragmentDoc}`;
export const AppointmentRequestFieldsFragmentDoc = gql`
    fragment AppointmentRequestFields on AppointmentRequest {
  id
  location
  status
  procedureBaseDefs {
    id
    marketplaceId
    duration
  }
  constraints {
    ...ConstraintFields
  }
}
    ${ConstraintFieldsFragmentDoc}`;
export const AppointmentFieldsFragmentDoc = gql`
    fragment AppointmentFields on Appointment {
  id
  start
  end
  duration
}
    `;
export const PractitionerParticipantFieldsFragmentDoc = gql`
    fragment PractitionerParticipantFields on Profile {
  title
  givenName
  familyName
  organization {
    phone
  }
}
    `;
export const ProfileAppointmentFieldsFragmentDoc = gql`
    fragment ProfileAppointmentFields on Appointment {
  ...AppointmentFields
  location
  startedAt
  completedAt
  status
  participants {
    type
    participant {
      ... on Profile {
        ...PractitionerParticipantFields
      }
    }
  }
  procedureBaseDefs {
    id
    marketplaceId
    duration
  }
}
    ${AppointmentFieldsFragmentDoc}
${PractitionerParticipantFieldsFragmentDoc}`;
export const MarketplaceFieldsFragmentDoc = gql`
    fragment MarketplaceFields on Marketplace {
  id
  name
  groupId
  createdAt
  navigationGroupId
  logo
  favicon
  primaryColor
  paymentCollectionMethod
  paymentDepositType
  paymentDepositValue
  hasPaymentPolicy
  paymentPolicyName
  paymentPolicyText
  requirePaymentPolicyAttestation
}
    `;
export const ProcedureBaseDefFieldsFragmentDoc = gql`
    fragment ProcedureBaseDefFields on ProcedureBaseDefinition {
  id
  marketplaceId
  name
  description
  duration
  thumbnail
  tags
  addOns
  tagline
  category
  ingredients
}
    `;
export const ProcedureBaseDefGroupFieldsFragmentDoc = gql`
    fragment ProcedureBaseDefGroupFields on ProcedureBaseDefinitionGroup {
  id
  name
  thumbnail
  banner
  description
  bgcolor
  fontColor
  procedureBaseDefs {
    id
    name
    tagline
    thumbnail
  }
  procedureBaseDefGroups {
    id
    name
    thumbnail
  }
  featuredBaseDefs {
    id
    name
    thumbnail
  }
}
    `;
export const DiscountFieldsFragmentDoc = gql`
    fragment DiscountFields on Discount {
  id
  percentage
  membershipDefinitionId
  procedureGroups {
    id
    name
  }
}
    `;
export const MembershipDefinitionFieldsFragmentDoc = gql`
    fragment MembershipDefinitionFields on MembershipDefinition {
  id
  name
  price
  period
  packageId
  marketplaceId
  advertise
  description
  list
  discounts {
    ...DiscountFields
  }
}
    ${DiscountFieldsFragmentDoc}`;
export const PackageItemDefinitionFieldsFragmentDoc = gql`
    fragment PackageItemDefinitionFields on PackageItemDefinition {
  id
  packageId
  points
  procedureGroups
}
    `;
export const PackageFieldsFragmentDoc = gql`
    fragment PackageFields on Package {
  id
  name
  price
  marketplaceId
  advertise
  description
  list
  packageItemDefinitions {
    ...PackageItemDefinitionFields
  }
}
    ${PackageItemDefinitionFieldsFragmentDoc}`;
export const FullMarketplaceFieldsFragmentDoc = gql`
    fragment FullMarketplaceFields on Marketplace {
  ...MarketplaceFields
  procedureBaseDefs {
    ...ProcedureBaseDefFields
  }
  organizations {
    ...OrganizationFields
  }
  procedureBaseDefGroups {
    ...ProcedureBaseDefGroupFields
  }
  membershipDefinitions {
    ...MembershipDefinitionFields
  }
  packages {
    ...PackageFields
  }
}
    ${MarketplaceFieldsFragmentDoc}
${ProcedureBaseDefFieldsFragmentDoc}
${OrganizationFieldsFragmentDoc}
${ProcedureBaseDefGroupFieldsFragmentDoc}
${MembershipDefinitionFieldsFragmentDoc}
${PackageFieldsFragmentDoc}`;
export const ProcedureBaseDefLayoutFieldsFragmentDoc = gql`
    fragment ProcedureBaseDefLayoutFields on ProcedureBaseDefinitionLayout {
  id
  type
  layout
  itemIndex
}
    `;
export const FullProcedureBaseDefFieldsFragmentDoc = gql`
    fragment FullProcedureBaseDefFields on ProcedureBaseDefinition {
  ...ProcedureBaseDefFields
  layouts {
    ...ProcedureBaseDefLayoutFields
  }
}
    ${ProcedureBaseDefFieldsFragmentDoc}
${ProcedureBaseDefLayoutFieldsFragmentDoc}`;
export const ProfileFieldsFragmentDoc = gql`
    fragment ProfileFields on Profile {
  id
}
    `;
export const AvailabilityFieldsFragmentDoc = gql`
    fragment AvailabilityFields on Availability {
  type
  start
  end
  duration
  tzid
  repeat {
    id
    frequency
    byWeekday
  }
}
    `;
export const FullProfileFieldsFragmentDoc = gql`
    fragment FullProfileFields on Profile {
  ...ProfileFields
  availableUntil
  availabilities {
    ...AvailabilityFields
  }
  procedureProfiles {
    procedureDefs {
      baseDefinitions {
        id
      }
    }
  }
}
    ${ProfileFieldsFragmentDoc}
${AvailabilityFieldsFragmentDoc}`;
export const MarketplaceGroupFieldsFragmentDoc = gql`
    fragment MarketplaceGroupFields on MarketplaceGroup {
  id
  label
  createdAt
}
    `;
export const ClientProfileFieldsFragmentDoc = gql`
    fragment ClientProfileFields on ClientProfile {
  id
  email
  familyName
  givenName
  phone
  address
  dob
  tzid
  sexAssignedAtBirth
  createdAt
  updatedAt
}
    `;
export const MembershipFieldsFragmentDoc = gql`
    fragment MembershipFields on Membership {
  id
  membershipDefinitionId
  renewalDate
  name
  status
}
    `;
export const PackageItemFieldsFragmentDoc = gql`
    fragment PackageItemFields on PackageItem {
  id
  packageItemDefinitionId
  balance
  packageName
  expiresAt
  totalPoints
  membershipId
  groupNames
}
    `;
export const PaymentInstrumentFieldsFragmentDoc = gql`
    fragment PaymentInstrumentFields on PaymentInstrument {
  id
  type
  processor
  brand
  lastFour
  expirationMonth
  expirationYear
}
    `;
export const MarketplaceUserFieldsFragmentDoc = gql`
    fragment MarketplaceUserFields on MarketplaceUser {
  id
  clientProfileId
  createdAt
  updatedAt
  primaryInstrumentId
  email
  emailConfirmed
  phone
  phoneConfirmed
  memberships {
    ...MembershipFields
  }
  packageItems {
    ...PackageItemFields
  }
  paymentInstruments {
    ...PaymentInstrumentFields
  }
}
    ${MembershipFieldsFragmentDoc}
${PackageItemFieldsFragmentDoc}
${PaymentInstrumentFieldsFragmentDoc}`;
export const CancelAppointmentDocument = gql`
    mutation CancelAppointment($input: CancelAppointmentInput!) {
  cancelAppointment(input: $input) {
    id
  }
}
    `;
export const CreateAppointmentRequestDocument = gql`
    mutation CreateAppointmentRequest($input: CreateAppointmentRequestInput!) {
  createAppointmentRequest(input: $input) {
    ...AppointmentRequestFields
  }
}
    ${AppointmentRequestFieldsFragmentDoc}`;
export const ProfileAppointmentsAndRequestsDocument = gql`
    query ProfileAppointmentsAndRequests($id: ID!) {
  marketplaceUser(id: $id) {
    appointments {
      ...ProfileAppointmentFields
    }
    appointmentRequests {
      ...AppointmentRequestFields
    }
  }
}
    ${ProfileAppointmentFieldsFragmentDoc}
${AppointmentRequestFieldsFragmentDoc}`;
export const MarketplaceDocument = gql`
    query Marketplace($id: ID!) {
  marketplace(id: $id) {
    ...FullMarketplaceFields
  }
}
    ${FullMarketplaceFieldsFragmentDoc}`;
export const MarketplaceGroupDocument = gql`
    query MarketplaceGroup($id: ID!) {
  marketplaceGroup(id: $id) {
    ...MarketplaceGroupFields
  }
}
    ${MarketplaceGroupFieldsFragmentDoc}`;
export const MarketplacesDocument = gql`
    query Marketplaces {
  marketplaces {
    ...MarketplaceFields
  }
}
    ${MarketplaceFieldsFragmentDoc}`;
export const OrganizationAvailabilitiesDocument = gql`
    query OrganizationAvailabilities($id: ID!, $page: AppointmentPageInput) {
  organization(id: $id) {
    profiles {
      ...FullProfileFields
      appointments(page: $page) {
        data {
          ...AppointmentFields
        }
      }
    }
  }
}
    ${FullProfileFieldsFragmentDoc}
${AppointmentFieldsFragmentDoc}`;
export const ProcedureBaseDefinitionDocument = gql`
    query ProcedureBaseDefinition($id: ID!) {
  procedureBaseDefinition(id: $id) {
    ...FullProcedureBaseDefFields
  }
}
    ${FullProcedureBaseDefFieldsFragmentDoc}`;
export const CreateApplePayInstrumentDocument = gql`
    mutation CreateApplePayInstrument($input: PaymentMethodInput!) {
  createApplePayInstrument(input: $input) {
    ...PaymentInstrumentFields
  }
}
    ${PaymentInstrumentFieldsFragmentDoc}`;
export const CreateApplePaySessionDocument = gql`
    mutation CreateApplePaySession($input: CreateApplePaySessionInput!) {
  createApplePaySession(input: $input)
}
    `;
export const CreatePaymentInstrumentDocument = gql`
    mutation CreatePaymentInstrument($input: PaymentMethodInput!) {
  createPaymentInstrument(input: $input) {
    ...PaymentInstrumentFields
  }
}
    ${PaymentInstrumentFieldsFragmentDoc}`;
export const DeletePaymentInstrumentDocument = gql`
    mutation DeletePaymentInstrument($id: ID!) {
  deletePaymentInstrument(id: $id)
}
    `;
export const PreviewCheckoutItemsDocument = gql`
    query PreviewCheckoutItems($input: PreviewCheckoutItemsInput!) {
  previewCheckoutItems(input: $input) {
    quantity
    price
    type
    description
  }
}
    `;
export const ValidatePromoCodeDocument = gql`
    query ValidatePromoCode($input: ValidatePromoCodeInput!) {
  validatePromoCode(input: $input) {
    valid
    error
    discountAmount
  }
}
    `;
export const CancelMembershipDocument = gql`
    mutation CancelMembership($id: ID!) {
  cancelMembership(id: $id)
}
    `;
export const ConfirmConsumerEmailDocument = gql`
    mutation ConfirmConsumerEmail($input: ConfirmConsumerEmailInput!) {
  confirmConsumerEmail(input: $input)
}
    `;
export const ConfirmConsumerPhoneDocument = gql`
    mutation ConfirmConsumerPhone($input: ConfirmConsumerPhoneInput!) {
  confirmConsumerPhone(input: $input)
}
    `;
export const CreateMarketplaceUserDocument = gql`
    mutation CreateMarketplaceUser($input: CreateMarketplaceUserInput!) {
  createMarketplaceUser(input: $input) {
    ...MarketplaceUserFields
  }
}
    ${MarketplaceUserFieldsFragmentDoc}`;
export const CreateMembershipDocument = gql`
    mutation CreateMembership($input: CreateMembershipInput!) {
  createMembership(input: $input) {
    ...MembershipFields
  }
}
    ${MembershipFieldsFragmentDoc}`;
export const GrantPackageDocument = gql`
    mutation GrantPackage($input: GrantPackageInput!) {
  grantPackage(input: $input) {
    ...PackageItemFields
  }
}
    ${PackageItemFieldsFragmentDoc}`;
export const ReactivateMembershipDocument = gql`
    mutation ReactivateMembership($id: ID!) {
  reactivateMembership(id: $id) {
    ...MembershipFields
  }
}
    ${MembershipFieldsFragmentDoc}`;
export const RequestConfirmConsumerEmailDocument = gql`
    mutation RequestConfirmConsumerEmail($input: RequestConfirmConsumerEmailInput!) {
  requestConfirmConsumerEmail(input: $input)
}
    `;
export const RequestConfirmConsumerPhoneDocument = gql`
    mutation RequestConfirmConsumerPhone($input: RequestConfirmConsumerPhoneInput!) {
  requestConfirmConsumerPhone(input: $input)
}
    `;
export const UpdateClientProfileDocument = gql`
    mutation UpdateClientProfile($input: UpdateClientProfileInput!) {
  updateClientProfile(input: $input) {
    ...ClientProfileFields
  }
}
    ${ClientProfileFieldsFragmentDoc}`;
export const UpdateMarketplaceUserDocument = gql`
    mutation UpdateMarketplaceUser($input: UpdateMarketplaceUserInput!) {
  updateMarketplaceUser(input: $input) {
    ...MarketplaceUserFields
  }
}
    ${MarketplaceUserFieldsFragmentDoc}`;
export const MarketplaceUserDocument = gql`
    query MarketplaceUser($id: ID!) {
  marketplaceUser(id: $id) {
    ...MarketplaceUserFields
    clientProfile {
      ...ClientProfileFields
    }
  }
}
    ${MarketplaceUserFieldsFragmentDoc}
${ClientProfileFieldsFragmentDoc}`;

export type SdkFunctionWrapper = <T>(action: (requestHeaders?:Record<string, string>) => Promise<T>, operationName: string, operationType?: string) => Promise<T>;


const defaultWrapper: SdkFunctionWrapper = (action, _operationName, _operationType) => action();

export function getSdk(client: GraphQLClient, withWrapper: SdkFunctionWrapper = defaultWrapper) {
  return {
    CancelAppointment(variables: CancelAppointmentMutationVariables, requestHeaders?: Dom.RequestInit["headers"]): Promise<CancelAppointmentMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<CancelAppointmentMutation>(CancelAppointmentDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'CancelAppointment', 'mutation');
    },
    CreateAppointmentRequest(variables: CreateAppointmentRequestMutationVariables, requestHeaders?: Dom.RequestInit["headers"]): Promise<CreateAppointmentRequestMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<CreateAppointmentRequestMutation>(CreateAppointmentRequestDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'CreateAppointmentRequest', 'mutation');
    },
    ProfileAppointmentsAndRequests(variables: ProfileAppointmentsAndRequestsQueryVariables, requestHeaders?: Dom.RequestInit["headers"]): Promise<ProfileAppointmentsAndRequestsQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<ProfileAppointmentsAndRequestsQuery>(ProfileAppointmentsAndRequestsDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'ProfileAppointmentsAndRequests', 'query');
    },
    Marketplace(variables: MarketplaceQueryVariables, requestHeaders?: Dom.RequestInit["headers"]): Promise<MarketplaceQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<MarketplaceQuery>(MarketplaceDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'Marketplace', 'query');
    },
    MarketplaceGroup(variables: MarketplaceGroupQueryVariables, requestHeaders?: Dom.RequestInit["headers"]): Promise<MarketplaceGroupQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<MarketplaceGroupQuery>(MarketplaceGroupDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'MarketplaceGroup', 'query');
    },
    Marketplaces(variables?: MarketplacesQueryVariables, requestHeaders?: Dom.RequestInit["headers"]): Promise<MarketplacesQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<MarketplacesQuery>(MarketplacesDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'Marketplaces', 'query');
    },
    OrganizationAvailabilities(variables: OrganizationAvailabilitiesQueryVariables, requestHeaders?: Dom.RequestInit["headers"]): Promise<OrganizationAvailabilitiesQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<OrganizationAvailabilitiesQuery>(OrganizationAvailabilitiesDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'OrganizationAvailabilities', 'query');
    },
    ProcedureBaseDefinition(variables: ProcedureBaseDefinitionQueryVariables, requestHeaders?: Dom.RequestInit["headers"]): Promise<ProcedureBaseDefinitionQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<ProcedureBaseDefinitionQuery>(ProcedureBaseDefinitionDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'ProcedureBaseDefinition', 'query');
    },
    CreateApplePayInstrument(variables: CreateApplePayInstrumentMutationVariables, requestHeaders?: Dom.RequestInit["headers"]): Promise<CreateApplePayInstrumentMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<CreateApplePayInstrumentMutation>(CreateApplePayInstrumentDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'CreateApplePayInstrument', 'mutation');
    },
    CreateApplePaySession(variables: CreateApplePaySessionMutationVariables, requestHeaders?: Dom.RequestInit["headers"]): Promise<CreateApplePaySessionMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<CreateApplePaySessionMutation>(CreateApplePaySessionDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'CreateApplePaySession', 'mutation');
    },
    CreatePaymentInstrument(variables: CreatePaymentInstrumentMutationVariables, requestHeaders?: Dom.RequestInit["headers"]): Promise<CreatePaymentInstrumentMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<CreatePaymentInstrumentMutation>(CreatePaymentInstrumentDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'CreatePaymentInstrument', 'mutation');
    },
    DeletePaymentInstrument(variables: DeletePaymentInstrumentMutationVariables, requestHeaders?: Dom.RequestInit["headers"]): Promise<DeletePaymentInstrumentMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<DeletePaymentInstrumentMutation>(DeletePaymentInstrumentDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'DeletePaymentInstrument', 'mutation');
    },
    PreviewCheckoutItems(variables: PreviewCheckoutItemsQueryVariables, requestHeaders?: Dom.RequestInit["headers"]): Promise<PreviewCheckoutItemsQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<PreviewCheckoutItemsQuery>(PreviewCheckoutItemsDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'PreviewCheckoutItems', 'query');
    },
    ValidatePromoCode(variables: ValidatePromoCodeQueryVariables, requestHeaders?: Dom.RequestInit["headers"]): Promise<ValidatePromoCodeQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<ValidatePromoCodeQuery>(ValidatePromoCodeDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'ValidatePromoCode', 'query');
    },
    CancelMembership(variables: CancelMembershipMutationVariables, requestHeaders?: Dom.RequestInit["headers"]): Promise<CancelMembershipMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<CancelMembershipMutation>(CancelMembershipDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'CancelMembership', 'mutation');
    },
    ConfirmConsumerEmail(variables: ConfirmConsumerEmailMutationVariables, requestHeaders?: Dom.RequestInit["headers"]): Promise<ConfirmConsumerEmailMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<ConfirmConsumerEmailMutation>(ConfirmConsumerEmailDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'ConfirmConsumerEmail', 'mutation');
    },
    ConfirmConsumerPhone(variables: ConfirmConsumerPhoneMutationVariables, requestHeaders?: Dom.RequestInit["headers"]): Promise<ConfirmConsumerPhoneMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<ConfirmConsumerPhoneMutation>(ConfirmConsumerPhoneDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'ConfirmConsumerPhone', 'mutation');
    },
    CreateMarketplaceUser(variables: CreateMarketplaceUserMutationVariables, requestHeaders?: Dom.RequestInit["headers"]): Promise<CreateMarketplaceUserMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<CreateMarketplaceUserMutation>(CreateMarketplaceUserDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'CreateMarketplaceUser', 'mutation');
    },
    CreateMembership(variables: CreateMembershipMutationVariables, requestHeaders?: Dom.RequestInit["headers"]): Promise<CreateMembershipMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<CreateMembershipMutation>(CreateMembershipDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'CreateMembership', 'mutation');
    },
    GrantPackage(variables: GrantPackageMutationVariables, requestHeaders?: Dom.RequestInit["headers"]): Promise<GrantPackageMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<GrantPackageMutation>(GrantPackageDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'GrantPackage', 'mutation');
    },
    ReactivateMembership(variables: ReactivateMembershipMutationVariables, requestHeaders?: Dom.RequestInit["headers"]): Promise<ReactivateMembershipMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<ReactivateMembershipMutation>(ReactivateMembershipDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'ReactivateMembership', 'mutation');
    },
    RequestConfirmConsumerEmail(variables: RequestConfirmConsumerEmailMutationVariables, requestHeaders?: Dom.RequestInit["headers"]): Promise<RequestConfirmConsumerEmailMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<RequestConfirmConsumerEmailMutation>(RequestConfirmConsumerEmailDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'RequestConfirmConsumerEmail', 'mutation');
    },
    RequestConfirmConsumerPhone(variables: RequestConfirmConsumerPhoneMutationVariables, requestHeaders?: Dom.RequestInit["headers"]): Promise<RequestConfirmConsumerPhoneMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<RequestConfirmConsumerPhoneMutation>(RequestConfirmConsumerPhoneDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'RequestConfirmConsumerPhone', 'mutation');
    },
    UpdateClientProfile(variables: UpdateClientProfileMutationVariables, requestHeaders?: Dom.RequestInit["headers"]): Promise<UpdateClientProfileMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<UpdateClientProfileMutation>(UpdateClientProfileDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'UpdateClientProfile', 'mutation');
    },
    UpdateMarketplaceUser(variables: UpdateMarketplaceUserMutationVariables, requestHeaders?: Dom.RequestInit["headers"]): Promise<UpdateMarketplaceUserMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<UpdateMarketplaceUserMutation>(UpdateMarketplaceUserDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'UpdateMarketplaceUser', 'mutation');
    },
    MarketplaceUser(variables: MarketplaceUserQueryVariables, requestHeaders?: Dom.RequestInit["headers"]): Promise<MarketplaceUserQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<MarketplaceUserQuery>(MarketplaceUserDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'MarketplaceUser', 'query');
    }
  };
}
export type Sdk = ReturnType<typeof getSdk>;