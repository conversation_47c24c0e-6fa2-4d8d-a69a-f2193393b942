import { Field, ID, ObjectType } from 'type-graphql';
import {
  MarketplaceFieldsFragment,
  OrganizationFieldsFragment,
  ProcedureBaseDefFieldsFragment,
  ProcedureBaseDefGroupFieldsFragment,
  MembershipDefinitionFieldsFragment,
  PackageFieldsFragment,
} from '../../../generated/dispatch-graphql';
import PaymentSettings from './PaymentSettings';

@ObjectType()
export default class Marketplace {
  @Field(() => ID)
  readonly id!: string;

  @Field()
  mgId!: string;

  @Field()
  name!: string;

  @Field(() => ID, { nullable: true })
  navigationGroupId?: string | null;

  @Field(() => String, { nullable: true })
  logo?: string | null;

  @Field(() => String, { nullable: true })
  favicon?: string | null;

  @Field(() => String, { nullable: true })
  primaryColor?: string | null;

  @Field(() => PaymentSettings, { nullable: true })
  paymentSettings?: PaymentSettings | null;

  organizations?: OrganizationFieldsFragment[];
  procedureBaseDefs?: ProcedureBaseDefFieldsFragment[];
  procedureBaseDefGroups?: ProcedureBaseDefGroupFieldsFragment[];
  membershipDefinitions?: MembershipDefinitionFieldsFragment[];
  packages?: PackageFieldsFragment[];

  constructor(
    data: MarketplaceFieldsFragment & {
      organizations?: OrganizationFieldsFragment[] | null;
      procedureBaseDefs?: ProcedureBaseDefFieldsFragment[] | null;
      procedureBaseDefGroups?: ProcedureBaseDefGroupFieldsFragment[] | null;
      membershipDefinitions?: MembershipDefinitionFieldsFragment[] | null;
      packages?: PackageFieldsFragment[] | null;
    },
  ) {
    this.id = data.id;
    this.mgId = data.groupId;
    this.name = data.name;
    this.navigationGroupId = data.navigationGroupId;
    this.logo = data.logo;
    this.favicon = data.favicon;
    this.primaryColor = data.primaryColor;

    this.paymentSettings = new PaymentSettings({
      paymentCollectionMethod: data.paymentCollectionMethod,
      paymentDepositType: data.paymentDepositType,
      paymentDepositValue: data.paymentDepositValue,
      hasPaymentPolicy: data.hasPaymentPolicy,
      paymentPolicyName: data.paymentPolicyName,
      paymentPolicyText: data.paymentPolicyText,
      requirePaymentPolicyAttestation: data.requirePaymentPolicyAttestation,
    });

    if (data.organizations) {
      this.organizations = data.organizations;
    }

    if (data.procedureBaseDefs) {
      this.procedureBaseDefs = data.procedureBaseDefs;
    }

    if (data.procedureBaseDefGroups) {
      this.procedureBaseDefGroups = data.procedureBaseDefGroups;
    }

    if (data.membershipDefinitions) {
      this.membershipDefinitions = data.membershipDefinitions;
    }

    if (data.packages) {
      this.packages = data.packages;
    }
  }
}
