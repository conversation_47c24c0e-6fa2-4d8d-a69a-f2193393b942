import BreadcrumbsHeader from '@/components/BreadcrumbsHeader';
import CardDataRow from '@/components/CardDataRow';
import Content from '@/components/Content';
import MuiLink from '@/components/Link';
import MarketplaceSettingsTabs from '@/components/MarketplaceSettingsTabs';
import UserFrame from '@/components/UserFrame';
import { RoleScope, useMarketplaceQuery } from '@/generated/graphql';
import { useAuthorize } from '@/hooks/authorize';
import { useProfile } from '@/hooks/profile';
import { useLayoutStyles } from '@/hooks/styles';
import {
  Box,
  Button,
  Card,
  CardContent,
  CardHeader,
  Typography,
} from '@material-ui/core';
import EditIcon from '@material-ui/icons/Edit';
import NextImage from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/router';

const listPermission = [
  'marketplaces:full',
  'marketplaces:list',
  {
    scope: RoleScope.Marketplace,
    permission: [],
  },
];

export default function Marketplace(): JSX.Element {
  const layoutClasses = useLayoutStyles();
  const router = useRouter();
  const id = router.query.id as string;

  const [canList] = useAuthorize(listPermission, { resourceId: id });

  const { profile } = useProfile();
  const { data: marketplaceData } = useMarketplaceQuery({
    variables: { id },
    skip: !id || !canList,
  });

  const marketplace = marketplaceData?.marketplace;

  const path = `/p/${profile?.pid}/root/marketplaces/${marketplace?.id}`;

  return (
    <UserFrame>
      <Content title={marketplace?.name ?? 'Marketplace'} maxWidth="md">
        <BreadcrumbsHeader backHref={`/p/${profile?.pid}/root/marketplaces`}>
          <MuiLink
            color="inherit"
            href={`/p/${profile?.pid}/root/marketplaces`}
          >
            Marketplaces
          </MuiLink>
          <Typography color="textPrimary">{marketplace?.name}</Typography>
        </BreadcrumbsHeader>
        <MarketplaceSettingsTabs selected="info" path={path} />
        <Card className={layoutClasses.card} variant="outlined">
          <CardHeader title="Marketplace Information" />
          <CardContent>
            <CardDataRow label="ID" value={marketplace?.id} />
            <CardDataRow label="Name" value={marketplace?.name} />
            {marketplace?.logo && (
              <CardDataRow
                label="Logo"
                value={
                  <Box display="flex" alignItems="center">
                    <Box
                      mr={1}
                      borderRadius="50%"
                      width={40}
                      height={40}
                      position="relative"
                    >
                      <NextImage
                        src={marketplace.logo}
                        layout="fill"
                        objectFit="cover"
                        alt={`${marketplace?.name} logo`}
                      />
                    </Box>
                    {/* <Typography variant="body2">Logo uploaded</Typography> */}
                  </Box>
                }
              />
            )}
            {marketplace?.favicon && (
              <CardDataRow
                label="Favicon"
                value={
                  <Box display="flex" alignItems="center">
                    <Box
                      mr={1}
                      width={24}
                      height={24}
                      position="relative"
                    >
                      <NextImage
                        src={marketplace.favicon}
                        layout="fill"
                        objectFit="contain"
                        alt={`${marketplace?.name} favicon`}
                      />
                    </Box>
                    {/* <Typography variant="body2">Favicon uploaded</Typography> */}
                  </Box>
                }
              />
            )}
            {marketplace?.primaryColor && (
              <CardDataRow
                label="Primary Color"
                value={
                  <Box display="flex" alignItems="center">
                    <div
                      style={{
                        width: 24,
                        height: 24,
                        borderRadius: '50%',
                        backgroundColor: marketplace.primaryColor,
                        marginRight: 8,
                        border: '1px solid #bdbdbd',
                      }}
                    />
                    <Typography variant="body2">
                      {marketplace.primaryColor}
                    </Typography>
                  </Box>
                }
              />
            )}
            {Boolean(marketplace) && (
              <>
                <CardDataRow
                  label="Require dispatch approval"
                  value={marketplace?.requireDispatchApproval ? 'Yes' : 'No'}
                />
                <CardDataRow
                  label="Require practitioner approval"
                  value={
                    marketplace?.requirePractitionerApproval ? 'Yes' : 'No'
                  }
                />
                {!!marketplace?.slackWebhookUrl && (
                  <CardDataRow
                    label="Slack webhook"
                    value={
                      marketplace.slackWebhookUrl.length > 40
                        ? `${marketplace.slackWebhookUrl.substring(0, 40)}...`
                        : marketplace.slackWebhookUrl
                    }
                  />
                )}
                {!!marketplace?.reviewsIoStoreId && (
                  <CardDataRow
                    label="Reviews.io Store ID"
                    value={marketplace.reviewsIoStoreId}
                  />
                )}
              </>
            )}
            <CardDataRow label="Group" value={marketplace?.group?.label} />
            <Box mt={3} display="flex" flexDirection="row">
              <Link href={`${path}/edit`} passHref>
                <Button
                  component="a"
                  variant="outlined"
                  color="primary"
                  startIcon={<EditIcon />}
                >
                  Edit settings
                </Button>
              </Link>
            </Box>
          </CardContent>
        </Card>
      </Content>
    </UserFrame>
  );
}
